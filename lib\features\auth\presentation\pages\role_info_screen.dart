/// -----
/// role_info_screen.dart
/// 
/// 角色信息页面，用于用户填写并提交角色认证信息
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/auth/presentation/bloc/role_info/role_info.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

class RoleInfoScreen extends StatefulWidget {
  final int roleIndex;
  final String roleTitle;
  final String userType;

  const RoleInfoScreen({
    Key? key,
    required this.roleIndex,
    required this.roleTitle,
    required this.userType,
  }) : super(key: key);

  @override
  State<RoleInfoScreen> createState() => _RoleInfoScreenState();
}

class _RoleInfoScreenState extends State<RoleInfoScreen> {
  // 表单控制器
  final _schoolController = TextEditingController();
  final _nameController = TextEditingController();
  final _idController = TextEditingController();

  // 加载状态
  bool _isLoading = false;

  // 根据角色获取ID字段标签
  String get _idFieldLabel {
    switch (widget.roleIndex) {
      case 0: // 学生
        return '学号';
      case 1: // 老师
        return '教工号';
      case 2: // 企业HR
        return '工号';
      default:
        return '编号';
    }
  }

  // 根据角色获取ID字段提示文本
  String get _idFieldHint {
    switch (widget.roleIndex) {
      case 0: // 学生
        return '请输入学号';
      case 1: // 老师
        return '请输入教育工号';
      case 2: // 企业HR
        return '请输入工号';
      default:
        return '请输入编号';
    }
  }



  // BLoC实例
  late final RoleInfoBloc _roleInfoBloc;
  
  @override
  void initState() {
    super.initState();
    // 初始化BLoC
    _roleInfoBloc = GetIt.instance<RoleInfoBloc>();
    
    // 监听BLoC状态变化
    _roleInfoBloc.stream.listen((state) {
      if (state is RoleInfoLoading) {
        setState(() {
          _isLoading = true;
        });
      } else if (state is RoleInfoAuthenticateSuccess) {
        setState(() {
          _isLoading = false;
        });
        _showSuccessSnackBar('认证成功');
        
        // 短暂延迟后跳转，让用户看到成功提示
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (!mounted) return;
          
          // 使用go_router跳转到首页
          GoRouter.of(context).go('/');
        });
      } else if (state is RoleInfoAuthenticateFailure) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar(state.message);
      }
    });
  }
  
  @override
  void dispose() {
    _schoolController.dispose();
    _nameController.dispose();
    _idController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
          backgroundColor: Colors.white,
          appBar: const CustomAppBar(
            title: '', // 移除AppBar中的标题
            showBackButton: true,
            centerTitle: false,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 70.h),
                  // 标题
                  Text(
                    '填写信息',
                    style: TextStyle(
                      fontSize: 46.sp, // 使用23sp的字体大小
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 54),

                  // 归属学校输入框
                  _buildInputField(
                    label: '归属学校',
                    hintText: '请输入并选择您的学校',
                    controller: _schoolController,
                  ),
                  const SizedBox(height: 20),

                  // 姓名输入框
                  _buildInputField(
                    label: '姓名',
                    hintText: '请输入您的姓名',
                    controller: _nameController,
                  ),
                  const SizedBox(height: 20),

                  _buildInputField(
                    label: _idFieldLabel,
                    hintText: _idFieldHint,
                    controller: _idController,
                  ),

                  const SizedBox(height: 50),

                  // 确认按钮
                  SizedBox(
                    width: double.infinity,
                    height: 88.h,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _validateAndSubmit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2979FF),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        disabledBackgroundColor: Colors.grey.shade400,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2.0,
                              ),
                            )
                          : Text(
                              '确认',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 30.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
  }

  // 验证表单并提交
  void _validateAndSubmit() {
    // 验证学校
    if (_schoolController.text.isEmpty) {
      _showErrorSnackBar('请输入归属学校');
      return;
    }

    // 验证姓名
    if (_nameController.text.isEmpty) {
      _showErrorSnackBar('请输入姓名');
      return;
    }

    // 验证ID
    if (_idController.text.isEmpty) {
      _showErrorSnackBar('请输入$_idFieldLabel');
      return;
    }

    // 调用BLoC进行认证
    _roleInfoBloc.add(
      AuthenticateEvent(
        deptName: _schoolController.text,
        userCode: _idController.text,
        userName: _nameController.text,
        userType: widget.userType,
      ),
    );
  }

  // 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  // 输入字段构建方法
  Widget _buildInputField({
    required String label,
    required String hintText,
    required TextEditingController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 30.sp,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(8), // 使用ClipRRect确保内容也被裁剪成圆角
          child: Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5), // 浅灰色背景，
              borderRadius: BorderRadius.circular(8), // 确保使用8px圆角
            ),
            child: TextField(
              controller: controller,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 24.sp,
                ),
                border: InputBorder.none, // 移除默认边框
                enabledBorder: InputBorder.none, // 移除启用状态边框
                focusedBorder: InputBorder.none, // 移除聚焦状态边框
                errorBorder: InputBorder.none, // 移除错误状态边框
                disabledBorder: InputBorder.none, // 移除禁用状态边框
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                isDense: true,
                filled: true,
                fillColor: const Color(0xFFF5F5F5), // 确保填充颜色一致
              ),
            ),
          ),
        ),
      ],
    );
  }
}
