/// -----
/// internship_application_screen.dart
///
/// 学生端实习申请页面，用于学生提交实习申请信息，包括企业信息、岗位信息、实习信息和附件材料
///
/// <AUTHOR>
/// @date 2025-05-22
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';
import 'package:flutter_demo/features/internship/data/models/company_info.dart';
import 'package:flutter_demo/features/internship/data/models/internship_info.dart';
import 'package:flutter_demo/features/internship/data/models/position_info.dart';
import 'package:flutter_demo/core/widgets/file_upload_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 实习申请页面
/// 
/// 学生提交实习申请信息，包括企业信息、岗位信息、实习信息和附件材料
/// 
/// 使用示例：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => InternshipApplicationScreen(
///       planId: '123',
///     ),
///   ),
/// );
/// ```
class InternshipApplicationScreen extends StatefulWidget {
  /// 实习计划ID
  /// 
  /// 用于关联实习申请与实习计划
  final String planId;
  
  /// 创建实习申请页面
  /// 
  /// @param key 小部件的键
  /// @param planId 实习计划ID，不能为空
  const InternshipApplicationScreen({
    super.key,
    required this.planId,
  });

  @override
  State<InternshipApplicationScreen> createState() =>
      _InternshipApplicationScreenState();
}

class _InternshipApplicationScreenState
    extends State<InternshipApplicationScreen> {
  // 企业信息
  late CompanyInfo _companyInfo;

  // 岗位信息
  late PositionInfo _positionInfo;

  // 实习信息
  late InternshipInfo _internshipInfo;

  // 附件文件
  File? _parentNoticeFile;
  File? _agreementFile;

  // 图片选择器
  final ImagePicker _picker = ImagePicker();

  // 实习类型选项
  final List<String> _internshipTypes = ['校内实习', '自主联系', '统一分配'];
  String _selectedInternshipType = '自主联系';

  // 岗位类别选项
  final List<String> _positionTypes = [
    '管理人员',
    '技术人员',
    '市场营销人员',
    '财务人员',
    '行政人员'
  ];
  String _selectedPositionType = '技术人员';

  // 专业匹配选项
  final List<String> _professionMatches = ['完全匹配', '基本匹配', '不匹配但接受'];
  String _selectedProfessionMatch = '基本匹配';

  @override
  void initState() {
    super.initState();
    // 初始化所有数据
    _companyInfo = CompanyInfo.sampleData();
    _positionInfo = PositionInfo.sampleData();
    _internshipInfo = InternshipInfo.sampleData();

    // 初始化选择的值
    _selectedInternshipType = _internshipInfo.internshipType;
    _selectedPositionType = _positionInfo.type;
    _selectedProfessionMatch = _internshipInfo.professionalMatch;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '实习申请',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                // 企业信息部分
                _buildCompanyInfoSection(),

                // 岗位信息部分
                _buildPositionInfoSection(),

                // 附件上传部分
                _buildAttachmentsSection(),
              ],
            ),
          ),

          // 提交按钮
          _buildSubmitButton(),
        ],
      ),
    );
  }

  /// 构建企业信息部分
  Widget _buildCompanyInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '企业信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 企业名称
        FormFieldItem(
          label: '企业名称',
          value: _companyInfo.name,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: value,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 统一社会信用代码
        FormFieldItem(
          label: '统一社会信用代码',
          value: _companyInfo.creditCode,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: value,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 企业规模
        FormFieldItem(
          label: '企业规模',
          value: _companyInfo.size,
          type: FormFieldType.dropdown,
          dropdownItems: const [
            '50人以下',
            '50-200人',
            '200-500人',
            '500-1000人',
            '1000人以上'
          ],
          onTap: () => _showDropdownDialog('企业规模', const [
            '50人以下',
            '50-200人',
            '200-500人',
            '500-1000人',
            '1000人以上'
          ], (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: value,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          }),
        ),

        // 企业性质
        FormFieldItem(
          label: '企业性质',
          value: _companyInfo.type,
          type: FormFieldType.dropdown,
          dropdownItems: const ['国有企业', '民营企业', '外资企业', '合资企业', '事业单位', '其他'],
          onTap: () => _showDropdownDialog(
              '企业性质', const ['国有企业', '民营企业', '外资企业', '合资企业', '事业单位', '其他'],
              (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: value,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          }),
        ),

        // 所属行业
        FormFieldItem(
          label: '所属行业',
          value: _companyInfo.industry,
          type: FormFieldType.dropdown,
          dropdownItems: const ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '其他'],
          onTap: () => _showDropdownDialog(
              '所属行业', const ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '其他'],
              (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: value,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          }),
        ),

        // 企业所在地
        FormFieldItem(
          label: '企业所在地',
          value: _companyInfo.location,
          type: FormFieldType.location,
          onTap: () {
            // TODO: 显示地址选择器
          },
        ),

        // 详细地址
        FormFieldItem(
          label: '详细地址',
          value: _companyInfo.address,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: value,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 联系人
        FormFieldItem(
          label: '联系人',
          value: _companyInfo.contactPerson,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: value,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 联系人电话
        FormFieldItem(
          label: '联系人电话',
          value: _companyInfo.contactPhone,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: value,
                email: _companyInfo.email,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 电子邮箱
        FormFieldItem(
          label: '电子邮箱',
          value: _companyInfo.email,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: value,
                zipCode: _companyInfo.zipCode,
              );
            });
          },
        ),

        // 邮政编码
        FormFieldItem(
          label: '邮政编码',
          value: _companyInfo.zipCode,
          type: FormFieldType.input,
          showDivider: false,
          onChanged: (value) {
            setState(() {
              _companyInfo = CompanyInfo(
                name: _companyInfo.name,
                creditCode: _companyInfo.creditCode,
                size: _companyInfo.size,
                type: _companyInfo.type,
                industry: _companyInfo.industry,
                location: _companyInfo.location,
                address: _companyInfo.address,
                contactPerson: _companyInfo.contactPerson,
                contactPhone: _companyInfo.contactPhone,
                email: _companyInfo.email,
                zipCode: value,
              );
            });
          },
        ),
      ],
    );
  }

  /// 构建岗位信息部分
  Widget _buildPositionInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '岗位信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 部门
        FormFieldItem(
          label: '部门',
          value: _positionInfo.department,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _positionInfo = PositionInfo(
                name: _positionInfo.name,
                department: value,
                type: _positionInfo.type,
                startDate: _positionInfo.startDate,
                endDate: _positionInfo.endDate,
                period: _positionInfo.period,
                daysPerWeek: _positionInfo.daysPerWeek,
                description: _positionInfo.description,
                supervisor: _positionInfo.supervisor,
                supervisorPhone: _positionInfo.supervisorPhone,
                positionCategory: _positionInfo.positionCategory,
                workContent: _positionInfo.workContent,
                location: _positionInfo.location,
                address: _positionInfo.address,
              );
            });
          },
        ),

        // 岗位名称
        FormFieldItem(
          label: '岗位名称',
          value: _positionInfo.name,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _positionInfo = PositionInfo(
                name: value,
                department: _positionInfo.department,
                type: _positionInfo.type,
                startDate: _positionInfo.startDate,
                endDate: _positionInfo.endDate,
                period: _positionInfo.period,
                daysPerWeek: _positionInfo.daysPerWeek,
                description: _positionInfo.description,
                supervisor: _positionInfo.supervisor,
                supervisorPhone: _positionInfo.supervisorPhone,
                positionCategory: _positionInfo.positionCategory,
                workContent: _positionInfo.workContent,
                location: _positionInfo.location,
                address: _positionInfo.address,
              );
            });
          },
        ),

        // 岗位类别
        FormFieldItem(
          label: '岗位类别',
          value: _selectedPositionType,
          type: FormFieldType.dropdown,
          dropdownItems: _positionTypes,
          onTap: () => _showDropdownDialog('岗位类别', _positionTypes, (value) {
            setState(() {
              _selectedPositionType = value;
              _positionInfo = PositionInfo(
                name: _positionInfo.name,
                department: _positionInfo.department,
                type: value,
                startDate: _positionInfo.startDate,
                endDate: _positionInfo.endDate,
                period: _positionInfo.period,
                daysPerWeek: _positionInfo.daysPerWeek,
                description: _positionInfo.description,
                supervisor: _positionInfo.supervisor,
                supervisorPhone: _positionInfo.supervisorPhone,
                positionCategory: _positionInfo.positionCategory,
                workContent: _positionInfo.workContent,
                location: _positionInfo.location,
                address: _positionInfo.address,
              );
            });
          }),
        ),

        // 专业匹配度
        FormFieldItem(
          label: '专业匹配度',
          value: _selectedProfessionMatch,
          type: FormFieldType.dropdown,
          dropdownItems: _professionMatches,
          onTap: () =>
              _showDropdownDialog('专业匹配度', _professionMatches, (value) {
            setState(() {
              _selectedProfessionMatch = value;
              _internshipInfo = InternshipInfo(
                startDate: _internshipInfo.startDate,
                endDate: _internshipInfo.endDate,
                internshipType: _internshipInfo.internshipType,
                professionalMatch: value,
                salary: _internshipInfo.salary,
                accommodationType: _internshipInfo.accommodationType,
                accommodationArea: _internshipInfo.accommodationArea,
                accommodationAddress: _internshipInfo.accommodationAddress,
                provideMeals: _internshipInfo.provideMeals,
                specialCircumstances: _internshipInfo.specialCircumstances,
              );
            });
          }),
        ),

        // 实习薪酬
        FormFieldItem(
          label: '实习薪酬',
          value: _internshipInfo.salary,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _internshipInfo = InternshipInfo(
                startDate: _internshipInfo.startDate,
                endDate: _internshipInfo.endDate,
                internshipType: _internshipInfo.internshipType,
                professionalMatch: _internshipInfo.professionalMatch,
                salary: value,
                accommodationType: _internshipInfo.accommodationType,
                accommodationArea: _internshipInfo.accommodationArea,
                accommodationAddress: _internshipInfo.accommodationAddress,
                provideMeals: _internshipInfo.provideMeals,
                specialCircumstances: _internshipInfo.specialCircumstances,
              );
            });
          },
        ),

        // 实习开始时间
        FormFieldItem(
          label: '实习开始时间',
          value: _internshipInfo.startDate,
          type: FormFieldType.date,
          onTap: () => _selectDate(true),
        ),

        // 实习结束时间
        FormFieldItem(
          label: '实习结束时间',
          value: _internshipInfo.endDate,
          type: FormFieldType.date,
          showDivider: false,
          onTap: () => _selectDate(false),
        ),
      ],
    );
  }

  /// 构建附件上传部分
  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 告家长通知书上传
        FileUploadItem(
          title: '告家长通知书',
          file: _parentNoticeFile,
          onTap: () => _pickFile('告家长通知书'),
        ),

        // 三方协议上传
        FileUploadItem(
          title: '三方协议',
          file: _agreementFile,
          onTap: () => _pickFile('三方协议'),
        ),

        SizedBox(height: 20.h),
      ],
    );
  }

  /// 构建提交按钮
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: ElevatedButton(
        onPressed: _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: EdgeInsets.symmetric(vertical: 24.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '提交',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// 处理提交操作
  void _handleSubmit() {
    // 验证表单
    if (_validateForm()) {
      AppSnackBar.showSuccess(context, '提交成功');
    }
  }

  /// 验证表单
  bool _validateForm() {
    // 企业信息验证
    if (_companyInfo.name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入企业名称')),
      );
      return false;
    }

    // 岗位信息验证
    if (_positionInfo.name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入岗位名称')),
      );
      return false;
    }

    // 实习时间验证
    if (_internshipInfo.startDate.isEmpty || _internshipInfo.endDate.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择实习时间')),
      );
      return false;
    }

    return true;
  }

  /// 选择日期
  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      final formattedDate = DateFormat('yyyy-MM-dd').format(picked);
      setState(() {
        if (isStartDate) {
          _internshipInfo = InternshipInfo(
            startDate: formattedDate,
            endDate: _internshipInfo.endDate,
            internshipType: _internshipInfo.internshipType,
            professionalMatch: _internshipInfo.professionalMatch,
            salary: _internshipInfo.salary,
            accommodationType: _internshipInfo.accommodationType,
            accommodationArea: _internshipInfo.accommodationArea,
            accommodationAddress: _internshipInfo.accommodationAddress,
            provideMeals: _internshipInfo.provideMeals,
            specialCircumstances: _internshipInfo.specialCircumstances,
          );
        } else {
          _internshipInfo = InternshipInfo(
            startDate: _internshipInfo.startDate,
            endDate: formattedDate,
            internshipType: _internshipInfo.internshipType,
            professionalMatch: _internshipInfo.professionalMatch,
            salary: _internshipInfo.salary,
            accommodationType: _internshipInfo.accommodationType,
            accommodationArea: _internshipInfo.accommodationArea,
            accommodationAddress: _internshipInfo.accommodationAddress,
            provideMeals: _internshipInfo.provideMeals,
            specialCircumstances: _internshipInfo.specialCircumstances,
          );
        }
      });
    }
  }

  /// 选择文件
  Future<void> _pickFile(String fileType) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile != null) {
        setState(() {
          if (fileType == '告家长通知书') {
            _parentNoticeFile = File(pickedFile.path);
          } else if (fileType == '三方协议') {
            _agreementFile = File(pickedFile.path);
          }
        });
      }
    } catch (e) {
      AppSnackBar.showError(context, '文件选择失败: $e');
    }
  }

  /// 显示下拉选择对话框（底部弹出式）
  void _showDropdownDialog(
      String title, List<String> items, Function(String) onSelected) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ),
          ),
          
          // 选项列表
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      title: Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontSize: 30.sp,
                            color: AppTheme.black333,
                          ),
                        ),
                      ),
                      onTap: () {
                        onSelected(items[index]);
                        Navigator.pop(context);
                      },
                    ),
                    Divider(height: 1.h, thickness: 1.h, color: AppTheme.dividerColor),
                  ],
                );
              },
            ),
          ),
          
          // 取消按钮
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
