import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/auth/presentation/pages/role_info_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'dart:math' as math;


class IdentityVerificationScreen extends StatefulWidget {
  const IdentityVerificationScreen({Key? key}) : super(key: key);

  @override
  State<IdentityVerificationScreen> createState() => _IdentityVerificationScreenState();
}

class _IdentityVerificationScreenState extends State<IdentityVerificationScreen> {
  // 存储选中的角色索引
  int? _selectedRoleIndex;

  // 角色数据
  final List<Map<String, dynamic>> _roles = [
    {
      'title': '我是学生',
      'icon': 'assets/images/student_icon.png',
      'userType': '1'
    },
    {
      'title': '我是老师',
      'icon': 'assets/images/teacher_icon.png',
      'userType': '2'
    },
    {
      'title': '我是企业HR',
      'icon': 'assets/images/hr_icon.png',
      'userType': '3'
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: '',
        showBackButton: false, // 不显示返回按钮，因为登录页面已经从导航栈中移除
        elevation: 0,
        titleFontSize: 0, // 不显示标题
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 158.h),
              // 标题 - 左对齐
              Text(
                '身份认证',
                style: TextStyle(
                  fontSize: 46.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              SizedBox(height: 28.h),
              // 副标题 - 左对齐
              Text(
                '请选择您的角色和输入基础信息',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.black54,
                ),
              ),
              SizedBox(height: 60.h),

              // 角色选择卡片
              ..._roles.asMap().entries.map((entry) {
                final index = entry.key;
                final role = entry.value;
                return _buildRoleCard(
                  title: role['title'],
                  iconPath: role['icon'],
                  isSelected: _selectedRoleIndex == index,
                  onTap: () {
                    setState(() {
                      _selectedRoleIndex = index;
                    });

                    // 导航到信息填写页面
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => RoleInfoScreen(
                          roleIndex: index,
                          roleTitle: role['title'],
                          userType: role['userType']
                        ),
                      ),
                    );
                  },
                );
              }).toList(),

              SizedBox(height: 30.h),
            ],
          ),
        ),
      ),
    );
  }

  // 角色卡片构建方法
  Widget _buildRoleCard({
    required String title,
    required String iconPath,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 200.h,
        margin: EdgeInsets.only(bottom: 40.h),
        decoration: BoxDecoration(
          color: const Color(0xFFE0E8FD),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
          child: Row(
            children: [
              // 角色图标
              ClipRRect(
                // borderRadius: BorderRadius.circular(8.r),
                child: Image.asset(
                  iconPath,
                  width: 242.w,
                  height: 178.h,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 81.w),
              // 角色标题
              Text(
                title,
                style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              // 右箭头图标
              // 使用自定义大小的箭头图标
              const Icon(
                Icons.chevron_right,
                color: Colors.black38,
                size: 26,
              ),
              // 添加右侧间距
              SizedBox(width: 30.w),
            ],
          ),
        ),
      ),
    );
  }
}