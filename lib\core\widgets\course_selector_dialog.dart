/// -----------------------------------------------------------------------------
/// course_selector_dialog.dart
///
/// 课程选择弹框组件，用于显示可选择的实习计划列表
/// 底部弹出式设计
///
/// <AUTHOR>
/// @date 2025-04-25
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_plan_list_item.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 课程选择底部弹框组件
class CourseSelectorBottomSheet extends StatelessWidget {
  /// 实习计划列表
  final List<InternshipPlanListItem> plans;

  /// 当前选中的计划索引
  final int selectedIndex;

  /// 计划选择回调
  final Function(int index, InternshipPlanListItem plan) onPlanSelected;

  const CourseSelectorBottomSheet({
    Key? key,
    required this.plans,
    required this.selectedIndex,
    required this.onPlanSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '请选择实习计划',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(
                    Icons.close,
                    size: 20,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),

          // 课程列表
          Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.5,
            ),
            child: ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 20),
              itemCount: plans.length,
              itemBuilder: (context, index) {
                final bool isSelected = index == selectedIndex;
                final plan = plans[index];
                return InkWell(
                  onTap: () {
                    onPlanSelected(index, plan);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFFECF1FF) : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : const Color(0xFFEEEEEE),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan.planName,
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelected ? AppTheme.primaryColor : Colors.black87,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${plan.semester} • ${plan.statusText}',
                          style: TextStyle(
                            fontSize: 12,
                            color: isSelected ? AppTheme.primaryColor.withOpacity(0.7) : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示课程选择底部弹框
Future<void> showCourseSelectorDialog({
  required BuildContext context,
  required List<InternshipPlanListItem> plans,
  required int selectedIndex,
  required Function(int index, InternshipPlanListItem plan) onPlanSelected,
}) async {
  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return CourseSelectorBottomSheet(
        plans: plans,
        selectedIndex: selectedIndex,
        onPlanSelected: onPlanSelected,
      );
    },
  );
}
