/// -----
/// safety_exam_screen.dart
/// 
/// 安全教育考试页面
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/safety/di/safety_injection.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/utils/exam_score_calculator.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_bloc.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_event.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_state.dart';
import 'package:flutter_demo/features/safety/presentation/widgets/exam_option_card.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 安全教育考试页面
///
/// 显示安全教育考试题目，允许用户选择答案并查看结果
/// 集成了BLoC提供者功能，用于路由系统
class SafetyExamScreen extends StatelessWidget {
  /// 路由名称
  static const routeName = '/safety-exam';

  /// 构造函数
  const SafetyExamScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SafetyExamBloc>(),
      child: _SafetyExamView(),
    );
  }
}

/// 安全教育考试视图
///
/// 实际的考试界面实现
class _SafetyExamView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '安全教育试题',
      ),
      body: BlocBuilder<SafetyExamBloc, SafetyExamState>(
        builder: (context, state) {
          if (state is SafetyExamInitial) {
            // 初始状态，加载题目
            context.read<SafetyExamBloc>().add(LoadExamQuestionsEvent());
            return const Center(child: CircularProgressIndicator());
          } else if (state is SafetyExamLoading) {
            // 加载中状态
            return const Center(child: CircularProgressIndicator());
          } else if (state is SafetyExamLoadFailure) {
            // 加载失败状态
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 48,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '加载试题失败',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(state.message),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<SafetyExamBloc>().add(LoadExamQuestionsEvent());
                    },
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          } else if (state is SafetyExamInProgress) {
            // 答题中状态
            return _buildExamInProgress(context, state);
          } else if (state is SafetyExamSaving) {
            // 保存考试记录中状态
            return _buildExamSaving(context, state);
          } else if (state is SafetyExamSaveFailure) {
            // 保存考试记录失败状态
            return _buildExamSaveFailure(context, state);
          } else if (state is SafetyExamCompleted) {
            // 考试完成状态
            return _buildExamCompleted(context, state);
          }
          
          // 默认返回加载中
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  /// 构建答题中的UI
  Widget _buildExamInProgress(BuildContext context, SafetyExamInProgress state) {
    final currentQuestion = state.currentQuestion;
    final questionType = _getQuestionTypeText(currentQuestion.type);
    final totalQuestions = state.questions.length;
    final currentQuestionNumber = state.currentQuestionIndex + 1;
    
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 25.w,vertical: 30.h),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 39.w,vertical: 44.h),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 题目类型和进度
                  Row(
                    children: [
                      Container(
                        width: 4,
                        height: 20,
                        color: AppTheme.primaryColor,
                        margin: const EdgeInsets.only(right: 8),
                      ),
                      Text(
                        questionType,
                        style: TextStyle(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.black333,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '$currentQuestionNumber/$totalQuestions',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 题号
                  Text(
                    'NO.$currentQuestionNumber',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // 题型标签
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: currentQuestion.isMultipleChoice
                          ? Colors.orange.shade100
                          : Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      currentQuestion.isMultipleChoice ? '多选题' : '单选题',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: currentQuestion.isMultipleChoice
                            ? Colors.orange.shade700
                            : Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 题目内容
                  Text(
                    currentQuestion.title,
                    style: TextStyle(
                      fontSize: 30.sp,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // 选项列表
                  ...currentQuestion.options.map((option) {
                    // 获取用户答案
                    final userAnswers = state.userAnswers[currentQuestion.id] ?? [];
                    final isSelected = userAnswers.contains(option.id);
                    final isCorrectOption = currentQuestion.correctAnswers.contains(option.id);

                    // 确定选项状态
                    OptionState optionState = OptionState.unselected;

                    if (state.isCurrentQuestionAnswered) {
                      if (isSelected) {
                        // 用户选择了这个选项
                        if (isCorrectOption) {
                          // 选择正确
                          optionState = OptionState.selectedCorrect;
                        } else {
                          // 选择错误
                          optionState = OptionState.selectedIncorrect;
                        }
                      } else if (isCorrectOption) {
                        // 这是正确答案，但用户没有选择它
                        optionState = OptionState.unselectedCorrect;
                      }
                    }

                    return ExamOptionCard(
                      option: option,
                      state: optionState,
                      isMultipleChoice: currentQuestion.isMultipleChoice,
                      isSelected: isSelected,
                      onTap: state.isCurrentQuestionAnswered
                          ? null
                          : () {
                        if (currentQuestion.isMultipleChoice) {
                          // 多选题
                          context.read<SafetyExamBloc>().add(
                            ToggleMultipleAnswerEvent(
                              optionId: option.id,
                            ),
                          );
                        } else {
                          // 单选题
                          context.read<SafetyExamBloc>().add(
                            SelectAnswerEvent(
                              selectedOptionId: option.id,
                            ),
                          );
                        }
                      },
                    );
                  }).toList(),

                  // 正确答案提示（仅在答错时显示）
                  if (state.isCurrentQuestionAnswered && !state.isCurrentAnswerCorrect)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Text(
                        '正确答案: ${currentQuestion.correctAnswers.join(', ')}',
                        style: TextStyle(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
        
        // 答题统计区域 - 点击显示答题卡
        GestureDetector(
          onTap: () {
            _showAnswerCardSheet(context, state);
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            padding: EdgeInsets.symmetric(horizontal: 39.w),
            height: 100.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 2,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF4CD964),
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${state.correctCount}',
                      style: TextStyle(
                        fontSize: 26.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    const Icon(
                      Icons.cancel,
                      color: Color(0xFFFF5252),
                      size: 20,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${state.incorrectCount}',
                      style: TextStyle(
                        fontSize: 26.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Text(
                  '$currentQuestionNumber/$totalQuestions',
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 60.h),
        // 按钮区域
        Container(
          margin: EdgeInsets.symmetric(horizontal: 25.w),
          padding: EdgeInsets.symmetric(vertical: 29.h),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: state.isCurrentQuestionAnswered
                  ? AppTheme.primaryColor
                  : Colors.grey.shade400, // 未回答时按钮为灰色
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.r),
              ),
            ),
            onPressed: state.isCurrentQuestionAnswered
                ? () {
              if (state.isLastQuestion) {
                // 最后一题，查看结果
                context.read<SafetyExamBloc>().add(ViewResultsEvent());
              } else {
                // 下一题
                context.read<SafetyExamBloc>().add(NextQuestionEvent());
              }
            }
                : null, // 未回答时按钮不可点击
            child: Text(
              state.isLastQuestion ? '查看结果' : '下一题',
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

      ],
    );
  }

  /// 构建考试完成的UI
  Widget _buildExamCompleted(BuildContext context, SafetyExamCompleted state) {
    // 使用分数计算器计算分数
    final score = ExamScoreCalculator.calculateScore(state.questions, state.userAnswers);
    final isPassed = score >= 60; // 60分及以上为合格
    
    // 根据合格状态选择颜色
    final Color statusColor = isPassed ? const Color(0xFF4CD964) : const Color(0xFFFF5252);
    final String statusText = isPassed ? '成绩合格' : '成绩不合格';
    final String imagePath = isPassed 
        ? 'assets/images/safefy_exam_qualified.png'
        : 'assets/images/safefy_exam_unqualified.png';
    
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 56.h),
            color: Colors.white,
            child: Column(
              children: [
                const SizedBox(height: 32),
                // 表情图标
                Image.asset(
                  imagePath,
                  width: 140.w,
                  height: 140.h,
                ),
                SizedBox(height: 39.h),
                // 合格/不合格状态文本
                Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                SizedBox(height: 65.h),
                // 分数说明文本
                Text(
                  '您的安全教育测试得分是',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: AppTheme. black999,
                  ),
                ),
                SizedBox(height: 32.h),
                // 分数显示
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      '$score',
                      style: TextStyle(
                        fontSize: 90.sp,
                        fontWeight: FontWeight.bold,
                        color: statusColor,
                      ),
                    ),
                    Text(
                      '分',
                      style: TextStyle(
                        fontSize: 26.sp,
                        color: statusColor,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 50.h),
                // 正确和错误题目统计
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 正确题目
                    Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Color(0xFF4CD964),
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${state.correctCount}',
                          style: TextStyle(
                            fontSize: 30.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 40),
                    // 错误题目
                    Row(
                      children: [
                        const Icon(
                          Icons.cancel,
                          color: Color(0xFFFF5252),
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${state.incorrectCount}',
                          style: TextStyle(
                            fontSize: 30.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 60.h), // 添加一些间距
          // 底部按钮
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.blue2165f6,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                ),
                onPressed: () {
                  // 重新开始考试
                  context.read<SafetyExamBloc>().add(LoadExamQuestionsEvent());
                },
                child: Text(
                  '再次测试',
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  // 不再需要此方法，已在_buildExamCompleted中直接实现

  /// 获取题目类型文本
  String _getQuestionTypeText(QuestionType type) {
    switch (type) {
      case QuestionType.singleChoice:
        return '单选题';
      case QuestionType.multipleChoice:
        return '多选题';
      // case QuestionType.trueFalse:
      //   return '判断题';
      default:
        return '题目';
    }
  }

  /// 显示答题卡底部弹框
  void _showAnswerCardSheet(BuildContext context, SafetyExamInProgress state) {
    final totalQuestions = state.questions.length;
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.7,
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 24.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题栏
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 4.w,
                        height: 20.h,
                        color: AppTheme.primaryColor,
                        margin: EdgeInsets.only(right: 8.w),
                      ),
                      Text(
                        '答题卡',
                        style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.black333,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      // 颜色指示
                      Row(
                        children: [
                          Container(
                            width: 14.w,
                            height: 14.h,
                            decoration: const BoxDecoration(
                              color: Color(0xFF4CD964),
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text('正确', style: TextStyle(fontSize: 22.sp, color: AppTheme.black333)),
                        ],
                      ),
                      SizedBox(width: 10.w),
                      Row(
                        children: [
                          Container(
                            width: 14.w,
                            height: 14.h,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFF5252),
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text('错误', style: TextStyle(fontSize: 22.sp, color: AppTheme.black333)),
                        ],
                      ),
                      SizedBox(width: 10.w),
                      Row(
                        children: [
                          Container(
                            width: 14.w,
                            height: 14.h,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text('未作答', style: TextStyle(fontSize: 22.sp, color: AppTheme.black333)),
                        ],
                      ),
                    ],
                  ),
                  // 关闭按钮
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(
                      Icons.close,
                      size: 21,
                      color: AppTheme.black333,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.h),
              
              // 网格布局显示题目
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 6,
                    childAspectRatio: 1.0,
                    crossAxisSpacing: 10.w,
                    mainAxisSpacing: 10.h,
                  ),
                  itemCount: totalQuestions,
                  itemBuilder: (context, index) {
                    final question = state.questions[index];
                    final questionId = question.id;
                    final userAnswers = state.userAnswers[questionId];
                    final isAnswered = userAnswers != null && userAnswers.isNotEmpty;

                    // 判断答案是否正确
                    bool isCorrect = false;
                    if (isAnswered) {
                      final correctAnswers = question.correctAnswers;
                      isCorrect = userAnswers.length == correctAnswers.length &&
                          correctAnswers.every(userAnswers.contains);
                    }
                    
                    // 根据答题情况决定颜色和样式
                    Color backgroundColor;
                    Color borderColor;
                    Color textColor;
                    
                    if (!isAnswered) {
                      // 未作答
                      backgroundColor = Colors.grey.shade300;
                      borderColor = Colors.grey.shade300;
                      textColor = Colors.white;
                    } else if (isCorrect) {
                      // 正确
                      backgroundColor = const Color(0xFFDDFFF1); // 浅绿色背景
                      borderColor = const Color(0xFF04AE68);    // 绿色边框
                      textColor = const Color(0xFF04AE68);      // 绿色文字
                    } else {
                      // 错误
                      backgroundColor = const Color(0xFFFFEBEB); // 浅红色背景
                      borderColor = const Color(0xFFFF4747);    // 红色边框
                      textColor = const Color(0xFFFF4747);      // 红色文字
                    }
                    
                    return GestureDetector(
                      onTap: () {
                        // 点击跳转到对应题目
                        if (index != state.currentQuestionIndex) {
                          Navigator.pop(context);
                          context.read<SafetyExamBloc>().add(
                            JumpToQuestionEvent(questionIndex: index),
                          );
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      child: Container(
                        width: 80.w,
                        height: 80.h,
                        margin: EdgeInsets.only(top:30.h),
                        decoration: BoxDecoration(
                          color: backgroundColor,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: borderColor,
                            width: 1.0,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: textColor,
                              fontSize: 36.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建保存考试记录中的UI
  Widget _buildExamSaving(BuildContext context, SafetyExamSaving state) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            '正在保存考试记录...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// 构建保存考试记录失败的UI
  Widget _buildExamSaveFailure(BuildContext context, SafetyExamSaveFailure state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '保存考试记录失败',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(state.message),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  // 重试保存
                  context.read<SafetyExamBloc>().add(SaveExamRecordEvent());
                },
                child: const Text('重试保存'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  // 跳过保存，直接显示结果
                  context.read<SafetyExamBloc>().add(LoadExamQuestionsEvent());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey,
                ),
                child: const Text('跳过保存'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
