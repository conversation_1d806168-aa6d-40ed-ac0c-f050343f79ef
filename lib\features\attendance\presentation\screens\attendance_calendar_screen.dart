/// -----
/// attendance_calendar_screen.dart
///
/// 签到日历页面，展示学生签到记录和日历
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/tag_widget.dart';
import 'package:flutter_demo/features/attendance/presentation/widgets/sign_location_row_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 签到状态枚举
enum AttendanceStatus {
  /// 已签到
  checked,
  /// 异常
  abnormal,
  /// 免签
  exempt,
  /// 未签到
  unchecked,
}

/// 签到记录模型
class AttendanceRecord {
  final DateTime date;
  final AttendanceStatus status;
  final String? checkInTime;
  final String? checkOutTime;
  final String? location;
  final String? remark;

  AttendanceRecord({
    required this.date,
    required this.status,
    this.checkInTime,
    this.checkOutTime,
    this.location,
    this.remark,
  });
}

/// 签到详情记录
class AttendanceDetail {
  final String time;
  final String type; // '上班' 或 '下班'
  final String status; // '正常' 或 '超时'
  final String location;
  final bool isAbnormal;

  AttendanceDetail({
    required this.time,
    required this.type,
    required this.status,
    required this.location,
    this.isAbnormal = false,
  });
}

/// 学期信息模型
class SemesterInfo {
  final String id;
  final String name;
  final String description;

  SemesterInfo({
    required this.id,
    required this.name,
    required this.description,
  });
}

/// 签到历史页面
class AttendanceCalendarScreen extends StatefulWidget {
  const AttendanceCalendarScreen({Key? key}) : super(key: key);

  @override
  State<AttendanceCalendarScreen> createState() => _AttendanceCalendarScreenState();
}

class _AttendanceCalendarScreenState extends State<AttendanceCalendarScreen> {
  late DateTime _selectedDay;
  late DateTime _focusedDay;
  late PageController _pageController;

  // Mock数据
  late Map<DateTime, AttendanceRecord> _attendanceData;
  late List<AttendanceDetail> _todayDetails;
  late SemesterInfo _selectedSemester;
  late List<SemesterInfo> _semesters;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _focusedDay = DateTime.now();
    _pageController = PageController();
    _initMockData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 初始化Mock数据
  void _initMockData() {
    final now = DateTime.now();

    // 学期数据
    _semesters = [
      SemesterInfo(
        id: '1',
        name: '2021级市场销售2023-2024实习学年第二学期',
        description: '岗位实习',
      ),
      SemesterInfo(
        id: '2',
        name: '2021级市场销售2023-2024实习学年第一学期',
        description: '岗位实习',
      ),
    ];
    _selectedSemester = _semesters.first;

    // 签到记录数据
    _attendanceData = {
      DateTime(now.year, now.month, 1): AttendanceRecord(
        date: DateTime(now.year, now.month, 1),
        status: AttendanceStatus.checked,
        checkInTime: '08:30',
        checkOutTime: '17:30',
        location: '广东省深圳市南山区',
      ),
      DateTime(now.year, now.month, 2): AttendanceRecord(
        date: DateTime(now.year, now.month, 2),
        status: AttendanceStatus.checked,
        checkInTime: '08:25',
        checkOutTime: '17:35',
        location: '广东省深圳市南山区',
      ),
      DateTime(now.year, now.month, 5): AttendanceRecord(
        date: DateTime(now.year, now.month, 5),
        status: AttendanceStatus.checked,
        checkInTime: '08:28',
        checkOutTime: '17:32',
        location: '广东省深圳市南山区',
      ),
      DateTime(now.year, now.month, 6): AttendanceRecord(
        date: DateTime(now.year, now.month, 6),
        status: AttendanceStatus.checked,
        checkInTime: '08:35',
        checkOutTime: '17:28',
        location: '广东省深圳市南山区',
      ),
      DateTime(now.year, now.month, 7): AttendanceRecord(
        date: DateTime(now.year, now.month, 7),
        status: AttendanceStatus.abnormal,
        checkInTime: '09:15',
        checkOutTime: '17:30',
        location: '广东省深圳市南山区',
        remark: '迟到',
      ),
      DateTime(now.year, now.month, 8): AttendanceRecord(
        date: DateTime(now.year, now.month, 8),
        status: AttendanceStatus.exempt,
        location: '广东省深圳市南山区',
        remark: '请假',
      ),
      DateTime(now.year, now.month, 9): AttendanceRecord(
        date: DateTime(now.year, now.month, 9),
        status: AttendanceStatus.exempt,
        location: '广东省深圳市南山区',
        remark: '周末',
      ),
    };

    // 今日详情数据
    _todayDetails = [
      AttendanceDetail(
        time: '08:30',
        type: '上班',
        status: '超范围',
        location: '距离地址:广东省深圳市南山区德白大厦1120号...距离500米',
        isAbnormal: true,
      ),
      AttendanceDetail(
        time: '08:30',
        type: '下班',
        status: '正常',
        location: '距离地址:广东省深圳市南山区德白大厦1120号...距离500米',
        isAbnormal: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '签到历史',
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 学期选择区域
          CourseHeaderSection(
            courseName: '${_selectedSemester.name} ${_selectedSemester.description}',
            availableCourses: _semesters.map((s) => '${s.name} ${s.description}').toList(),
            onCourseChanged: (newCourse) {
              // 处理学期切换逻辑
              final index = _semesters.indexWhere((s) => '${s.name} ${s.description}' == newCourse);
              if (index >= 0) {
                setState(() {
                  _selectedSemester = _semesters[index];
                });
              }
            },
          ),

          // 月份切换区域
          _buildMonthSelector(),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 日历组件
                  _buildCalendar(),

                  // 状态说明
                  _buildStatusLegend(),

                  // 位置信息
                  _buildLocationInfo(),

                  // 签到记录列表
                  _buildAttendanceList(),

                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ],
      ),
      // 右下角"今"悬浮按钮
      floatingActionButton: FloatingActionButton(
        onPressed: _goToToday,
        backgroundColor: const Color(0xFF2165F6),
        child: Text(
          '今',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }



  /// 构建月份选择器
  Widget _buildMonthSelector() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => _changeMonth(-1),
            child: Icon(
              Icons.chevron_left,
              size: 48.w,
              color: const Color(0xFF333333),
            ),
          ),
          Text(
            '${_focusedDay.year}年${_focusedDay.month}月',
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          GestureDetector(
            onTap: () => _changeMonth(1),
            child: Icon(
              Icons.chevron_right,
              size: 48.w,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  /// 切换月份
  void _changeMonth(int delta) {
    setState(() {
      _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + delta);
    });
  }

  /// 跳转到今天
  void _goToToday() {
    final today = DateTime.now();
    setState(() {
      _selectedDay = today;
      _focusedDay = today;
    });
  }

  /// 构建日历组件
  Widget _buildCalendar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: TableCalendar<AttendanceRecord>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
        eventLoader: (day) {
          final record = _attendanceData[DateTime(day.year, day.month, day.day)];
          return record != null ? [record] : [];
        },
        startingDayOfWeek: StartingDayOfWeek.sunday,
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF333333),
          ),
          defaultTextStyle: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF333333),
          ),
          selectedTextStyle: TextStyle(
            fontSize: 28.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          todayTextStyle: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF333333),
          ),
          selectedDecoration: const BoxDecoration(
            color: Color(0xFF2165F6),
            shape: BoxShape.circle,
          ),
          todayDecoration: const BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          defaultDecoration: const BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          weekendDecoration: const BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          markerDecoration: const BoxDecoration(
            color: Colors.transparent,
          ),
        ),
        headerStyle: const HeaderStyle(
          formatButtonVisible: false,
          titleCentered: false,
          leftChevronVisible: false,
          rightChevronVisible: false,
          headerPadding: EdgeInsets.zero,
          titleTextStyle: TextStyle(fontSize: 0),
        ),
        daysOfWeekStyle: DaysOfWeekStyle(
          weekdayStyle: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black333,
            fontWeight: FontWeight.w500,
          ),
          weekendStyle: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black333,
            fontWeight: FontWeight.w500,
          ),
        ),
        daysOfWeekHeight: 40.h,
        calendarBuilders: CalendarBuilders(
          dowBuilder: (context, day) {
            final weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            return Center(
              child: Text(
                weekdays[day.weekday % 7],
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black333,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          },
          defaultBuilder: (context, day, focusedDay) {
            final record = _attendanceData[DateTime(day.year, day.month, day.day)];
            return _buildCalendarDay(day, record?.status ?? AttendanceStatus.unchecked);
          },
          selectedBuilder: (context, day, focusedDay) {
            final record = _attendanceData[DateTime(day.year, day.month, day.day)];
            return _buildCalendarDay(day, record?.status ?? AttendanceStatus.unchecked, isSelected: true);
          },
          todayBuilder: (context, day, focusedDay) {
            final record = _attendanceData[DateTime(day.year, day.month, day.day)];
            return _buildCalendarDay(day, record?.status ?? AttendanceStatus.unchecked);
          },
        ),
        onDaySelected: (selectedDay, focusedDay) {
          setState(() {
            _selectedDay = selectedDay;
            _focusedDay = focusedDay;
          });
        },
        onPageChanged: (focusedDay) {
          setState(() {
            _focusedDay = focusedDay;
          });
        },
      ),
    );
  }

  /// 构建日历日期
  Widget _buildCalendarDay(DateTime day, AttendanceStatus status, {bool isSelected = false}) {
    Color? backgroundColor;
    Color textColor = AppTheme.black333;

    // 根据签到状态设置颜色
    switch (status) {
      case AttendanceStatus.checked:
        backgroundColor = const Color(0xFF08DF87); // 绿色
        textColor = Colors.white;
        break;
      case AttendanceStatus.abnormal:
        backgroundColor = const Color(0xFFFFCC53); // 橙色
        textColor = Colors.white;
        break;
      case AttendanceStatus.exempt:
        backgroundColor = const Color(0xFF91A7FF); // 蓝色
        textColor = Colors.white;
        break;
      case AttendanceStatus.unchecked:
        backgroundColor = isSelected ? const Color(0xFF91A7FF) : null;
        textColor = isSelected ? Colors.white : AppTheme.black333;
        break;
    }

    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(10.r), // 圆角正方形
      ),
      child: Center(
        child: Text(
          '${day.day}',
          style: TextStyle(
            fontSize: 28.sp,
            color: textColor,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// 构建状态说明
  Widget _buildStatusLegend() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '温馨提示：左右滑动可快速切换月份',
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '如出现签到异常请联系班主任或者进行补签',
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              _buildLegendItem('签到', const Color(0xFF00C851)),
              SizedBox(width: 40.w),
              _buildLegendItem('异常', const Color(0xFFFF8800)),
              SizedBox(width: 40.w),
              _buildLegendItem('免签', const Color(0xFF91A7FF)),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16.w,
          height: 16.w,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          label,
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF333333),
          ),
        ),
      ],
    );
  }

  /// 构建位置信息
  Widget _buildLocationInfo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          const SignLocationRowWidget(address: '当前位置：广东省深圳市南山区德白大厦1120号...'),
          SizedBox(height: 20.h),
          Row(
            children: [
              const TagWidget(tag:'超范围'),
              SizedBox(width: 8.w),
              Text(
                '偏移单位安全范围60km',
                style: TextStyle(
                  fontSize: 22.sp,
                  color: const Color(0xFFFF4747),
                ),
                )
            ],
          ),
          SizedBox(height: 54.h),
          // 补签按钮
          _buildMakeUpButton(),
          SizedBox(height: 30.h),
        ],
      ),
    );
  }

  /// 构建补签按钮
  Widget _buildMakeUpButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      child: Center(
        child: GestureDetector(
          onTap: () {
            // 处理补签逻辑
            _showMakeUpDialog();
          },
          child: Container(
            width: 200.w,
            height: 200.w,
            decoration: const BoxDecoration(
              color: Color(0xFF2165F6),
              shape: BoxShape.circle,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '补签',
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  '08:30',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建签到记录列表
  Widget _buildAttendanceList() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      child: Column(
        children: _todayDetails.map((detail) => _buildAttendanceItem(detail)).toList(),
      ),
    );
  }

  /// 构建签到记录项
  Widget _buildAttendanceItem(AttendanceDetail detail) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                detail.type,
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                detail.time,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                ),
              ),
              SizedBox(width: 16.w),
              TagWidget(
                tag: detail.status,
                backgroundColor: detail.isAbnormal ? const Color(0xFFFF4747) : const Color(0xFF04AE68),
              ),
              if (detail.isAbnormal) ...[
                SizedBox(width: 16.w),
                Text(
                  '偏移单位安全范围60km',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: const Color(0xFFFF4747),
                  ),
                )
              ], // 如果不显示则返回空widget
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            detail.location,
            style: TextStyle(
              fontSize: 22.sp,
              color: AppTheme.black999,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示补签对话框
  void _showMakeUpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('补签确认'),
        content: const Text('确定要进行补签操作吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 处理补签逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('补签成功')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}


