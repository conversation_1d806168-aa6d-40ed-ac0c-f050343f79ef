/// -----
/// teacher_internship_plan_list_screen.dart
///
/// 教师端实习计划列表页面，展示所有实习计划
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:flutter_demo/core/router/route_constants.dart';
import 'package:flutter_demo/core/services/auth_expiry_service.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/empty_state_widget.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_plan.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/teacher_plan_list/teacher_plan_list_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/teacher_plan_list/teacher_plan_list_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/teacher_plan_list/teacher_plan_list_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class TeacherInternshipPlanListScreen extends StatelessWidget {
  const TeacherInternshipPlanListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<TeacherPlanListBloc>()
        ..add(const LoadTeacherPlanListEvent()),
      child: const TeacherInternshipPlanListView(),
    );
  }
}

class TeacherInternshipPlanListView extends StatefulWidget {
  const TeacherInternshipPlanListView({Key? key}) : super(key: key);

  @override
  State<TeacherInternshipPlanListView> createState() =>
      _TeacherInternshipPlanListViewState();
}

class _TeacherInternshipPlanListViewState
    extends State<TeacherInternshipPlanListView> {
  late final AuthExpiryService _authExpiryService;

  @override
  void initState() {
    super.initState();
    _authExpiryService = GetIt.instance<AuthExpiryService>();
  }

  void _onRefresh() {
    context.read<TeacherPlanListBloc>().add(const RefreshTeacherPlanListEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(title: '实习计划'),
      body: BlocConsumer<TeacherPlanListBloc, TeacherPlanListState>(
        listener: (context, state) {
          if (state is TeacherPlanListRefreshSuccessState) {
            AppSnackBar.showSuccess(context, '刷新成功');
          } else if (state is TeacherPlanListRefreshErrorState) {
            AppSnackBar.showError(context, state.message);
          } else if (state is TeacherPlanListAuthFailureState) {
            // 处理认证失败，自动跳转到登录页面
            _authExpiryService.handleAuthExpiry(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          if (state is TeacherPlanListLoadingState) {
            return const LoadingWidget(message: '正在加载实习计划...');
          }

          if (state is TeacherPlanListAuthFailureState) {
            // 认证失败状态，显示登录提示
            return EmptyStateWidget(
              icon: Icons.lock_outline,
              title: '认证失败',
              message: '登录已过期，请重新登录',
              buttonText: '去登录',
              onButtonPressed: () => context.go(AppRoutes.login),
            );
          }

          if (state is TeacherPlanListErrorState) {
            return EmptyStateWidget(
              icon: Icons.error_outline,
              title: '加载失败',
              message: state.message,
              buttonText: '重试',
              onButtonPressed: () => context
                  .read<TeacherPlanListBloc>()
                  .add(const LoadTeacherPlanListEvent()),
            );
          }

          if (state is TeacherPlanListLoadedState) {
            final plans = state.plans;

            if (plans.isEmpty) {
              return EmptyStateWidget(
                icon: Icons.inbox,
                title: '暂无实习计划',
                message: '当前没有实习计划记录',
                buttonText: '刷新',
                onButtonPressed: _onRefresh,
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                _onRefresh();
                // 等待刷新完成
                await Future.delayed(const Duration(milliseconds: 500));
              },
              child: _buildPlanList(plans),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildPlanList(List<InternshipPlan> plans) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      itemCount: plans.length,
      itemBuilder: (context, index) {
        final plan = plans[index];
        return _buildPlanItem(plan);
      },
    );
  }

  Widget _buildPlanItem(InternshipPlan plan) {
    final statusColor =
        plan.statusText == '进行中' ? const Color(0xFF2165F6) : Colors.grey;

    return InkWell(
        onTap: () {
          // 点击整个卡片跳转到详情页，并传递 planId
          context.push(
              AppRoutes.teacherInternshipPlanDetail.replaceFirst(':planId', plan.id.toString()));
        },
        borderRadius: BorderRadius.circular(20.r),
        child: Container(
          margin: EdgeInsets.only(bottom: 20.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态
              Padding(
                padding: EdgeInsets.fromLTRB(30.w, 28.h, 30.w, 28.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        plan.planName,
                        style: TextStyle(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                      child: Text(
                        plan.statusText,
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 分割线
              Divider(
                height: 1.h,
                thickness: 1.h,
                color: const Color(0xFFEEEEEE),
                indent: 20.w,
                endIndent: 20.w,
              ),

              // 详细信息
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 28.h),
                child: Column(
                  children: [
                    _buildInfoRow('实习类型', plan.typeText),
                    SizedBox(height: 16.h),
                    _buildInfoRow('实习周期', plan.formattedPeriod),
                    SizedBox(height: 16.h),
                    _buildInfoRow('实习学期', plan.semester),
                    SizedBox(height: 16.h),
                    _buildInfoRow('创建人', plan.createPerson),
                  ],
                ),
              ),

              // 底部信息
              Padding(
                padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 20.h),
                child: Row(
                  children: [
                    Text(
                      '创建时间: ${plan.formattedCreateTime}',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    // 移除了单独的查看按钮，因为整个卡片都可以点击了
                    Row(
                      children: [
                        Text(
                          '查看',
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: AppTheme.black999,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 24.sp,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 160.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black999,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black333,
            ),
          ),
        ),
      ],
    );
  }
}
