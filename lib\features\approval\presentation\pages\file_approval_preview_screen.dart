/// -----
/// file_approval_preview_screen.dart
///
/// 文件审批预览页面，提供文件预览和审批功能
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/approval/core/enums/approval_enums.dart';
import 'package:flutter_demo/features/approval/domain/entities/student_file_approval.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'dart:io';

/// 文件审批预览页面
class FileApprovalPreviewScreen extends StatefulWidget {
  const FileApprovalPreviewScreen({
    required this.fileDetail,
    required this.studentName,
    super.key,
  });

  /// 文件详情对象
  final FileApprovalDetail fileDetail;

  /// 学生姓名
  final String studentName;

  @override
  State<FileApprovalPreviewScreen> createState() => _FileApprovalPreviewScreenState();
}

class _FileApprovalPreviewScreenState extends State<FileApprovalPreviewScreen> {
  /// 是否正在处理通过操作
  bool _isApprovingProcessing = false;

  /// 是否正在处理驳回操作
  bool _isRejectingProcessing = false;

  /// PDF文件路径
  String? _pdfPath;

  /// 是否正在加载PDF
  bool _isLoadingPdf = true;

  /// PDF加载错误信息
  String? _pdfError;



  /// PDF总页数
  int _totalPages = 0;

  /// 当前页码
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  /// 根据fileStatus获取ApprovalStatus
  ApprovalStatus get _approvalStatus {
    switch (widget.fileDetail.fileStatus) {
      case 0: // 未上传
      case 1: // 已上传
        return ApprovalStatus.pending;
      case 2: // 已审核
        return ApprovalStatus.approved;
      case 3: // 已驳回
        return ApprovalStatus.rejected;
      default:
        return ApprovalStatus.pending;
    }
  }

  @override
  void dispose() {
    // 清理临时PDF文件
    _cleanupTempFile();
    super.dispose();
  }

  /// 清理临时PDF文件
  void _cleanupTempFile() {
    if (_pdfPath != null && _pdfPath!.contains('temp_pdf_')) {
      try {
        final file = File(_pdfPath!);
        if (file.existsSync()) {
          file.deleteSync();
        }
      } on Exception {
        // 忽略清理错误
      }
    }
  }

  /// 加载PDF文件
  Future<void> _loadPdf() async {
    try {
      setState(() {
        _isLoadingPdf = true;
        _pdfError = null;
      });

      final fileUrl = widget.fileDetail.fileUrl;
      if (fileUrl.isEmpty) {
        setState(() {
          _pdfError = 'PDF文件URL为空';
          _isLoadingPdf = false;
        });
        return;
      }

      // 从网络下载PDF文件
      final dioClient = GetIt.instance<DioClient>();
      final response = await dioClient.dioInstance.get(
        fileUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // 将PDF文件写入临时目录
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf');
      await tempFile.writeAsBytes(response.data);

      setState(() {
        _pdfPath = tempFile.path;
        _isLoadingPdf = false;
      });
    } on Exception catch (e) {
      setState(() {
        _pdfError = '加载PDF文件失败: ${e.toString()}';
        _isLoadingPdf = false;
      });
    }
  }

  /// 处理审批通过
  Future<void> _handleApprove() async {
    if (_isApprovingProcessing) {
      return;
    }

    setState(() {
      _isApprovingProcessing = true;
    });

    try {
      // 调用审批通过API
      final dioClient = GetIt.instance<DioClient>();
      await dioClient.post(
        'internshipservice/v1/internship/teacher/file/approve',
        data: {
          'fileStatus': 2, // 2表示通过
          'id': widget.fileDetail.id,
          'remark': '审批通过',
        },
      );

      if (mounted) {
        AppSnackBar.showSuccess(context, '审批通过成功');
        context.pop(true); // 返回true表示审批状态已改变
      }
    } on Exception catch (e) {
      if (mounted) {
        AppSnackBar.showError(context, '审批失败：${e.toString()}');
      }
    } finally {
      setState(() {
        _isApprovingProcessing = false;
      });
    }
  }

  /// 处理审批驳回
  Future<void> _handleReject() async {
    if (_isRejectingProcessing) {
      return;
    }

    // 显示驳回原因输入对话框
    final reason = await _showRejectReasonDialog();
    if (reason == null || reason.trim().isEmpty) {
      return;
    }

    setState(() {
      _isRejectingProcessing = true;
    });

    try {
      // 调用审批驳回API
      final dioClient = GetIt.instance<DioClient>();
      await dioClient.post(
        'internshipservice/v1/internship/teacher/file/approve',
        data: {
          'fileStatus': 3, // 3表示驳回
          'id': widget.fileDetail.id,
          'remark': reason,
        },
      );

      if (mounted) {
        AppSnackBar.showSuccess(context, '审批驳回成功');
        context.pop(true); // 返回true表示审批状态已改变
      }
    } on Exception catch (e) {
      if (mounted) {
        AppSnackBar.showError(context, '驳回失败：${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRejectingProcessing = false;
        });
      }
    }
  }

  /// 显示驳回原因输入对话框
  Future<String?> _showRejectReasonDialog() async {
    final TextEditingController controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('驳回原因'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: '请输入驳回原因...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final reason = controller.text.trim();
              if (reason.isEmpty) {
                if (mounted) {
                  AppSnackBar.showError(context, '请输入驳回原因');
                }
                return;
              }
              Navigator.of(context).pop(reason);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: widget.fileDetail.fileName,
      ),
      body: Column(
        children: [

          // PDF显示区域 - 使用Expanded自动填充剩余空间
          Expanded(
            child: _buildPdfDisplayArea(),
          ),

          // 底部操作按钮区域
          if (_approvalStatus != ApprovalStatus.rejected && _pdfError == null)
            _buildBottomActionArea(),
        ],
      ),
    );
  }


  /// 构建PDF显示区域
  Widget _buildPdfDisplayArea() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 32.w),
      child: Stack(
        children: [
          // PDF内容
          _buildPdfView(),

          // 审批印章覆盖层
          if (_approvalStatus != ApprovalStatus.pending)
            _buildApprovalStamp(),
        ],
      ),
    );
  }

  /// 构建底部操作区域
  Widget _buildBottomActionArea() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.fromLTRB(32.w, 10.h, 32.w, 32.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: _buildActionButtons(),
      ),
    );
  }



  /// 构建PDF视图
  Widget _buildPdfView() {
    // 如果正在加载PDF
    if (_isLoadingPdf) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60.w,
              height: 60.w,
              child: const CircularProgressIndicator(
                strokeWidth: 3,
              ),
            ),
            SizedBox(height: 32.h),
            Text(
              '正在加载PDF文件...',
              style: TextStyle(
                fontSize: 28.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // 如果有错误信息，显示错误提示
    if (_pdfError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80.w,
              color: Colors.red[300],
            ),
            SizedBox(height: 24.h),
            Text(
              '文件加载失败',
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.w500,
                color: Colors.red[600],
              ),
            ),
            SizedBox(height: 16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Text(
                _pdfError!,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 32.h),
            ElevatedButton(
              onPressed: _loadPdf,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2165F6),
                padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                '重新加载',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // 显示PDF视图
    if (_pdfPath != null) {
      return Column(
        children: [
          // PDF视图容器 - 使用Expanded填充剩余空间
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: PDFView(
                filePath: _pdfPath!,
                onRender: (pages) {
                  // PDF渲染完成
                  setState(() {
                    _totalPages = pages ?? 0;
                  });
                },
                onError: (error) {
                  setState(() {
                    _pdfError = 'PDF显示错误: $error';
                  });
                },
                onPageError: (page, error) {
                  setState(() {
                    _pdfError = '页面 $page 显示错误: $error';
                  });
                },
                onPageChanged: (page, total) {
                  setState(() {
                    _currentPage = page ?? 0;
                    _totalPages = total ?? 0;
                  });
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  // PDF视图控制器创建完成
                },
              ),
            ),
          ),

          // 页面指示器
          if (_totalPages > 0)
            Container(
              margin: EdgeInsets.only(top: 16.h),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                '${_currentPage + 1} / $_totalPages',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      );
    }

    return Center(
      child: Text(
        '无法显示PDF文件',
        style: TextStyle(
          fontSize: 28.sp,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  /// 构建审批印章
  Widget _buildApprovalStamp() {
    return Positioned(
      top: 120.h,
      right: 80.w,
      child: Image.asset(
        _approvalStatus == ApprovalStatus.approved
            ? 'assets/images/passed_icon.png'
            : 'assets/images/rejected_icon.png',
        width: 120.w,
        height: 120.w,
        fit: BoxFit.contain,
      ),
    );
  }



  /// 构建操作按钮
  Widget _buildActionButtons() {
    if (_approvalStatus == ApprovalStatus.pending) {
      // 待审批状态：显示驳回和通过按钮
      return Row(
        children: [
          // 驳回按钮
          Expanded(
            child: _buildRejectButton(),
          ),

          SizedBox(width: 32.w),

          // 通过按钮
          Expanded(
            child: _buildApproveButton(),
          ),
        ],
      );
    } else if (_approvalStatus == ApprovalStatus.approved) {
      // 已通过状态：只显示驳回按钮
      return _buildRejectButton();
    }

    return const SizedBox.shrink();
  }

  /// 构建驳回按钮
  Widget _buildRejectButton() {
    return Container(
      height: 88.h,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.red, width: 2.w),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isRejectingProcessing ? null : _handleReject,
          borderRadius: BorderRadius.circular(8.r),
          child: Center(
            child: _isRejectingProcessing
                ? SizedBox(
                    width: 40.w,
                    height: 40.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                    ),
                  )
                : Text(
                    '驳回',
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// 构建通过按钮
  Widget _buildApproveButton() {
    return Container(
      height: 88.h,
      decoration: BoxDecoration(
        color: const Color(0xFF2165F6),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isApprovingProcessing ? null : _handleApprove,
          borderRadius: BorderRadius.circular(8.r),
          child: Center(
            child: _isApprovingProcessing
                ? SizedBox(
                    width: 40.w,
                    height: 40.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    '通过',
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
