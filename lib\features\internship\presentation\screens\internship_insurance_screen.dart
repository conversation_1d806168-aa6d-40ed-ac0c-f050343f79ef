/// -----
/// internship_insurance_screen.dart
/// 
/// 实习保险页面，显示学生的实习保险信息
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_item.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:intl/intl.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/domain/entities/insurance_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/insurance_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/insurance_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/insurance_state.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/document_viewer_widget.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_section_card.dart';

/// 实习保险页面
///
/// 显示学生的实习保险信息，包括保险名称、保险单号、购买方等信息
class InternshipInsuranceScreen extends StatelessWidget {
  const InternshipInsuranceScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => InsuranceBloc()
        ..add(const LoadInsuranceInfoEvent(
          courseId: '2021级市场销售2023-2024实习学年第二学期',
        )),
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: const CustomAppBar(
          title: '我的实习保险',
          showBackButton: true,
        ),
        body: BlocBuilder<InsuranceBloc, InsuranceState>(
          builder: (context, state) {
            if (state is InsuranceLoading) {
              return const Center(child: CircularProgressIndicator());
            } else if (state is InsuranceLoaded) {
              return _buildContent(context, state);
            } else if (state is InsuranceError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, size: 48, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(state.message),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        context.read<InsuranceBloc>().add(
                          const LoadInsuranceInfoEvent(
                            courseId: '2021级市场销售2023-2024实习学年第二学期',
                          ),
                        );
                      },
                      child: const Text('重试'),
                    ),
                  ],
                ),
              );
            }
            /*else if (state is InsuranceContractViewing) {
              return DocumentViewerWidget(
                filePath: state.filePath,
                fileName: state.fileName,
              );
            }*/
            
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  /// 构建页面内容
  Widget _buildContent(BuildContext context, InsuranceLoaded state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 课程头部
          CourseHeaderSection(
            courseName: state.currentCourse,
            initialExpanded: false,
            availableCourses: state.availableCourses,
            onCourseChanged: (newCourse) {
              context.read<InsuranceBloc>().add(
                ChangeCourseEvent(newCourseId: newCourse),
              );
            },
          ),
          
          const SizedBox(height: 12),
          
          // 实习保险信息卡片
          InfoSectionCard(
            title: '实习保险信息',
            icon: Image.asset('assets/images/insurance_icon.png',width: 26.w, height: 33.h, ),
            child: _buildInsuranceInfo(context, state.insuranceInfo),
          ),
        ],
      ),
    );
  }

  /// 构建保险信息内容
  Widget _buildInsuranceInfo(BuildContext context, InsuranceInfo info) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    
    return Column(
      children: [
        InfoItem(
          label: '保险名称',
          value: info.name,
        ),
        InfoItem(
          label: '保险单号',
          value: info.policyNumber,
        ),
        InfoItem(
          label: '保险购买方',
          value: info.purchaser,
        ),
        InfoItem(
          label: '保险种类',
          value: info.type,
        ),
        InfoItem(
          label: '保险起止时间',
          value: '${dateFormat.format(info.insurancePeriod.start)} 至 ${dateFormat.format(info.insurancePeriod.end)}',
        ),
        InfoItem(
          label: '实习起止时间',
          value: '${dateFormat.format(info.internshipPeriod.start)} 至 ${dateFormat.format(info.internshipPeriod.end)}',
        ),
        InfoItem(
          label: '未覆盖天数',
          value: '${info.uncoveredDays}天未覆盖',
          isHighlighted: true,
        ),
        // 保险合同文件卡片
        if (info.contractFileName != null)
          _buildContractFileItem(context, info),
          SizedBox(height: 30.h),
      ],
    );
  }

  /// 构建合同文件项
  Widget _buildContractFileItem(BuildContext context, InsuranceInfo info) {
    return Container(
      height: 88.h,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.description,
            color: Colors.blue,
          ),
        ),
        title: Text(
          info.contractFileName ?? '保险合同',
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                // 显示更多操作菜单
                showModalBottomSheet(
                  context: context,
                  builder: (context) => Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.download),
                        title: const Text('下载文件'),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('下载功能开发中')),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.share),
                        title: const Text('分享文件'),
                        onTap: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('分享功能开发中')),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
        onTap: () {
          if (info.contractFilePath != null) {
            context.read<InsuranceBloc>().add(
              ViewInsuranceContractEvent(
                filePath: info.contractFilePath!,
                fileName: info.contractFileName ?? '保险合同',
              ),
            );
          }
        },
      ),
    );
  }
}
