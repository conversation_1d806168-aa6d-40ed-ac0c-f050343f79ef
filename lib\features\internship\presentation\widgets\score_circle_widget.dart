/// -----
/// score_circle_widget.dart
///
/// 评分圆环组件，用于展示实习成绩评分
/// 基于统一的圆环进度组件重构
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/progress_ring/progress_ring.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 评分圆环组件
///
/// 用于展示实习成绩评分，包括圆环进度条、分数和评分标题
/// 基于 UnifiedProgressRing 重构，保持向后兼容性
class ScoreCircleWidget extends StatelessWidget {
  /// 分数值
  final int score;

  /// 最大分数值
  final int maxScore;

  /// 评分标题
  final String title;

  /// 圆环大小
  final double size;

  /// 圆环线宽
  final double strokeWidth;

  /// 圆环颜色
  final Color progressColor;

  /// 圆环背景色
  final Color backgroundColor;

  /// 分数文本样式
  final TextStyle? scoreTextStyle;

  /// 标题文本样式
  final TextStyle? titleTextStyle;

  /// 是否启用动画
  final bool enableAnimation;

  const ScoreCircleWidget({
    Key? key,
    required this.score,
    required this.title,
    this.maxScore = 100,
    this.size = 160,
    this.strokeWidth = 10,
    this.progressColor = AppTheme.primaryColor,
    this.backgroundColor = const Color(0xFFEEEEEE),
    this.scoreTextStyle,
    this.titleTextStyle,
    this.enableAnimation = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progress = maxScore > 0 ? score / maxScore : 0.0;

    // 默认分数文本样式
    final defaultScoreTextStyle = TextStyle(
      fontSize: 113.sp,
      fontWeight: FontWeight.bold,
      color: AppTheme.primaryColor,
      height: 0.9,
    );

    // 默认标题文本样式
    final defaultTitleTextStyle = TextStyle(
      fontSize: 22.sp,
      color: AppTheme.black999,
      height: 1.1,
    );

    return UnifiedProgressRing(
      progress: progress,
      mainText: score.toString(),
      subtitle: title,
      size: size,
      strokeWidth: strokeWidth,
      progressColor: progressColor,
      backgroundColor: backgroundColor,
      mainTextStyle: scoreTextStyle ?? defaultScoreTextStyle,
      subtitleStyle: titleTextStyle ?? defaultTitleTextStyle,
      enableAnimation: enableAnimation,
    );
  }
}
