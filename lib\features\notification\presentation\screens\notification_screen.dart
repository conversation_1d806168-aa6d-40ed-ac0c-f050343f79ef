/// -----------------------------------------------------------------------------
/// notification_screen.dart
///
/// 消息通知页面，用于展示系统消息、公告和报告提醒等各类通知
///
/// 功能：
/// 1. 展示不同类别的通知消息（系统消息、学校公告、院系公告等）
/// 2. 支持按全部/未读消息进行筛选
/// 3. 提供一键将所有消息标记为已读的功能
/// 4. 显示每条消息的未读状态和时间
///
/// 使用方法：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(builder: (context) => const NotificationScreen()),
/// );
/// ```
///
/// <AUTHOR>
/// @version 2.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/notification/presentation/screens/notification_detail_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:intl/intl.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 消息通知页面
///
/// 展示用户的所有通知消息，包括系统消息、学校公告、院系公告等
/// 支持按全部/未读消息进行筛选，并提供一键将所有消息标记为已读的功能
class NotificationScreen extends StatefulWidget {
  const NotificationScreen({Key? key}) : super(key: key);

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

/// 消息通知页面状态管理类
///
/// 管理消息列表的状态和筛选逻辑，包括全部/未读消息的切换和标记已读功能
class _NotificationScreenState extends State<NotificationScreen> {
  // 每页显示的数量
  static const _pageSize = 3;

  // 分页控制器
  final PagingController<int, NotificationCategory> _pagingController =
      PagingController(firstPageKey: 1);

  // 消息分类数据源
  final List<NotificationCategory> _allCategories = [
    NotificationCategory(
      title: '系统消息',
      icon: Image.asset('assets/images/notinotifications_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 1,
      messages: [
        NotificationMessage(
          content: '亲爱的 周平樟，您的账号已通过申请，请及时完成...',
          date: '2025.05.12 13:00',
          isRead: false,
        ),
      ],
    ),
    NotificationCategory(
      title: '学校公告',
      icon: Image.asset('assets/images/campaign_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 1,
      messages: [
        NotificationMessage(
          content: '本周实习计划暂停，具体时间请关注学校官方网站...',
          date: '2025.03.15 13:00',
          isRead: false,
        ),
      ],
    ),
    NotificationCategory(
      title: '院系公告',
      icon: Image.asset('assets/images/account_balance_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 0,
      messages: [
        NotificationMessage(
          content: '本院系此次实习计划审批已完成，请参加实习的学...',
          date: '2025.03.15 13:00',
          isRead: true,
        ),
      ],
    ),
    NotificationCategory(
      title: '周报',
      icon: Image.asset('assets/images/weekly_report_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 0,
      messages: [
        NotificationMessage(
          content: '本周有1个实习生未提交周报',
          date: '2025.03.15 13:00',
          isRead: true,
        ),
      ],
    ),
    NotificationCategory(
      title: '日报',
      icon: Image.asset('assets/images/daily_report_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 0,
      messages: [
        NotificationMessage(
          content: '今日有12个实习生未提交日报',
          date: '2025.03.15 13:00',
          isRead: true,
        ),
      ],
    ),
    NotificationCategory(
      title: '月报',
      icon: Image.asset('assets/images/month_report_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 0,
      messages: [
        NotificationMessage(
          content: '今日有6个实习生未提交月报',
          date: '2025.03.15 13:00',
          isRead: true,
        ),
      ],
    ),
    NotificationCategory(
      title: '总结',
      icon: Image.asset('assets/images/campaign_outlined_icon.png',width: 88.w,height: 88.w),
      unreadCount: 0,
      messages: [
        NotificationMessage(
          content: '有4个实习生未提交总结',
          date: '2025.03.15 13:00',
          isRead: true,
        ),
      ],
    ),
  ];

  bool showAllMessages = true;
  int totalUnread = 0;

  @override
  void initState() {
    super.initState();
    _calculateTotalUnread();
    _pagingController.addPageRequestListener(_fetchPage);

    // 监听筛选状态变化
    _pagingController.addStatusListener((status) {
      if (status == PagingStatus.subsequentPageError) {
        AppSnackBar.showWithAction(
          context,
          '加载失败，请重试',
          actionLabel: '重试',
          onPressed: () => _pagingController.retryLastFailedRequest(),
        );
      }
    });
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  void _calculateTotalUnread() {
    totalUnread = _allCategories.fold(0, (sum, category) => sum + category.unreadCount);
  }

  void _markAllAsRead() {
    setState(() {
      for (var category in _allCategories) {
        for (var message in category.messages) {
          message.isRead = true;
        }
        category.unreadCount = 0;
      }
      _calculateTotalUnread();
      _pagingController.refresh(); // 刷新列表
    });
  }

  // 获取指定页的数据
  Future<void> _fetchPage(int pageKey) async {
    try {
      // 模拟网络请求延迟
      await Future.delayed(const Duration(milliseconds: 800));

      // 计算当前页的数据范围
      final startIndex = (pageKey - 1) * _pageSize;
      final endIndex = startIndex + _pageSize;

      // 获取当前页的数据
      List<NotificationCategory> items = [];

      // 根据筛选条件获取数据
      final filteredCategories = showAllMessages
          ? _allCategories
          : _allCategories.where((c) => c.unreadCount > 0).toList();

      // 如果起始索引超出范围，返回空列表
      if (startIndex >= filteredCategories.length) {
        items = [];
      } else {
        // 计算实际的结束索引（防止越界）
        final actualEndIndex = endIndex > filteredCategories.length
            ? filteredCategories.length
            : endIndex;

        // 获取当前页的数据
        items = filteredCategories.sublist(startIndex, actualEndIndex);
      }

      // 判断是否还有更多数据
      final isLastPage = endIndex >= filteredCategories.length;

      if (isLastPage) {
        _pagingController.appendLastPage(items);
      } else {
        _pagingController.appendPage(items, pageKey + 1);
      }
    } catch (error) {
      _pagingController.error = error;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppBar(
        title: '消息',
        centerTitle: true,
        backgroundColor: Colors.white,
        titleColor: AppTheme.textPrimaryColor,
        titleFontSize: 18,
        titleFontWeight: FontWeight.w600,
        backIconColor: AppTheme.textPrimaryColor,
        actions: [
          // 添加垃圾桶图标
          IconButton(
            icon: const Icon(
              Icons.delete_outline,
              color: AppTheme.textPrimaryColor,
            ),
            onPressed: () {
              // 处理删除操作
              AppSnackBar.show(context, '清空消息功能待实现');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildNotificationHeader(),
          Expanded(
            child: _buildNotificationList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // 自定义选项卡
          _buildCustomTabBar(),


        ],
      ),
    );
  }

  // 自定义选项卡
  Widget _buildCustomTabBar() {
    return Container(
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
        children: [
          // 全部选项
          _buildTabItem(true, '全部', null),

          const SizedBox(width: 80), // 添加间距，使两个选项卡之间有一定距离

          // 未读选项
          _buildTabItem(false, '未读', totalUnread),
        ],
      ),
    );
  }

  // 选项卡项
  Widget _buildTabItem(bool isAllTab, String title, int? count) {
    final bool isSelected = (isAllTab && showAllMessages) || (!isAllTab && !showAllMessages);

    return GestureDetector(
      onTap: () {
        setState(() {
          showAllMessages = isAllTab;
          _pagingController.refresh(); // 刷新列表
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          border: isSelected
              ? const Border(
                  bottom: BorderSide(
                    color: AppTheme.primaryColor,
                    width: 2.0,
                  ),
                )
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? AppTheme.primaryColor : Colors.grey.shade600,
              ),
            ),
            if (count != null && count > 0) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '$count',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }


  Widget _buildNotificationList() {
    return RefreshIndicator(
      onRefresh: () => Future.sync(() => _pagingController.refresh()),
      color: AppTheme.primaryColor,
      child: PagedListView<int, NotificationCategory>(
        pagingController: _pagingController,
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.only(top: 0, bottom: 16),
        builderDelegate: PagedChildBuilderDelegate<NotificationCategory>(
          itemBuilder: (context, category, index) {
            return _buildNotificationItem(category);
          },
          firstPageErrorIndicatorBuilder: (context) => _buildErrorIndicator(),
          newPageErrorIndicatorBuilder: (context) => _buildErrorIndicator(),
          firstPageProgressIndicatorBuilder: (context) => _buildLoadingIndicator(),
          newPageProgressIndicatorBuilder: (context) => _buildLoadingIndicator(),
          noItemsFoundIndicatorBuilder: (context) => _buildEmptyIndicator(),
          noMoreItemsIndicatorBuilder: (context) => _buildNoMoreItemsIndicator(),
        ),
      ),
    );
  }

  // 错误指示器
  Widget _buildErrorIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 32,
            ),
            const SizedBox(height: 8),
            const Text('加载失败'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _pagingController.refresh(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  // 加载指示器
  Widget _buildLoadingIndicator() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: CircularProgressIndicator(),
      ),
    );
  }

  // 空数据指示器
  Widget _buildEmptyIndicator() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.notifications_off_outlined,
              color: Colors.grey[400],
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              showAllMessages ? '暂无消息' : '暂无未读消息',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 没有更多数据指示器
  Widget _buildNoMoreItemsIndicator() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: Text(
          '没有更多消息了',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationItem(NotificationCategory category) {
    final bool hasUnread = category.unreadCount > 0;
    final message = category.messages.first;

    return Material(
      color: Colors.white,
      child: InkWell(
        onTap: () {
          // 处理点击通知项的逻辑
          _navigateToDetail(category, message);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头像图标
              _buildCategoryAvatar(category, hasUnread),
              const SizedBox(width: 16),

              // 内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题行
                    Row(
                      children: [
                        Text(
                          category.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: hasUnread ? FontWeight.w600 : FontWeight.w500,
                            color: hasUnread ? AppTheme.textPrimaryColor : Colors.black87,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          _formatDate(message.date),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // 消息内容
                    Text(
                      message.content,
                      style: TextStyle(
                        fontSize: 14,
                        color: hasUnread ? AppTheme.textSecondaryColor : Colors.grey.shade600,
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 格式化日期
  String _formatDate(String dateStr) {
    try {
      // 假设原始格式为 "yyyy.MM.dd HH:mm"
      final parts = dateStr.split(' ');
      if (parts.length == 2) {
        // 将yyyy.MM.dd格式转换为yyyy-MM-dd格式
        final dateParts = parts[0].split('.');
        if (dateParts.length == 3) {
          return '${dateParts[0]}-${dateParts[1]}-${dateParts[2]}';
        }
      }
      return dateStr;
    } catch (e) {
      return dateStr;
    }
  }

  // 导航到消息详情页
  void _navigateToDetail(NotificationCategory category, NotificationMessage message) {
    // 如果消息未读，标记为已读
    if (!message.isRead) {
      setState(() {
        message.isRead = true;
        category.unreadCount = category.unreadCount > 0 ? category.unreadCount - 1 : 0;
        _calculateTotalUnread();
      });
    }

    // 导航到详情页
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NotificationDetailScreen(
          title: category.title,
          date: message.date,
          content: message.content,
          publisher: _getPublisherByCategory(category.title),
        ),
      ),
    );
  }

  // 根据分类获取发布单位
  String _getPublisherByCategory(String category) {
    switch (category) {
      case '系统消息':
        return '亿硕教育';
      case '学校公告':
        return '教务处';
      case '院系公告':
        return '建筑工程学院';
      case '日报':
        return '实习管理系统';
      case '周报':
        return '实习管理系统';
      default:
        return '亿硕教育';
    }
  }

  // 构建分类头像
  Widget _buildCategoryAvatar(NotificationCategory category, bool hasUnread) {
    // 根据分类获取不同的颜色
    Color avatarColor = _getCategoryColor(category.title);

    return Stack(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: avatarColor,
            shape: BoxShape.circle,
          ),
          child: Icon(
            _getCategoryIcon(category.title),
            color: Colors.white,
            size: 24,
          ),
        ),
        if (hasUnread)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
          ),
      ],
    );
  }

  // 根据分类获取图标
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case '系统消息':
        return Icons.notifications_outlined;
      case '学校公告':
        return Icons.campaign_outlined;
      case '院系公告':
        return Icons.account_balance_outlined;
      case '日报':
        return Icons.calendar_today_outlined;
      case '周报':
        return Icons.date_range_outlined;
      case '月报':
        return Icons.calendar_month_outlined;
      case '总结':
        return Icons.summarize_outlined;
      default:
        return Icons.notifications_outlined;
    }
  }

  // 根据分类获取颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case '系统消息':
        return Colors.blue;
      case '学校公告':
        return Colors.purple.shade300;
      case '院系公告':
        return Colors.indigo.shade300;
      case '日报':
        return Colors.green.shade400;
      case '周报':
        return Colors.amber.shade400;
      case '月报':
        return Colors.orange.shade300;
      case '总结':
        return Colors.lightBlue;
      default:
        return Colors.blue;
    }
  }
}

/// 消息分类模型
///
/// 表示一类消息，如系统消息、学校公告等
/// 包含分类标题、图标、未读数量和消息列表
class NotificationCategory {
  /// 分类标题（如“系统消息”、“学校公告”等）
  final String title;

  /// 分类图标
  final Widget icon;

  /// 未读消息数量
  int unreadCount;

  /// 该分类下的消息列表
  final List<NotificationMessage> messages;

  NotificationCategory({
    required this.title,
    required this.icon,
    required this.unreadCount,
    required this.messages,
  });
}

/// 消息模型
///
/// 表示单条消息，包含消息内容、日期和读取状态
class NotificationMessage {
  /// 消息内容
  final String content;

  /// 消息日期（格式为 "yyyy.MM.dd HH:mm"）
  final String date;

  /// 是否已读
  bool isRead;

  NotificationMessage({
    required this.content,
    required this.date,
    required this.isRead,
  });
}
