/// -----
/// free_internship_approval_detail_screen.dart
///
/// 免实习申请详情页面，用于查看和审批免实习申请
///
/// <AUTHOR>
/// @date 2025-06-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:get_it/get_it.dart';

import 'package:flutter_demo/core/common/image_viewer_screen.dart';
import 'package:flutter_demo/core/mixins/page_logging_mixin.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/user_avatar.dart';
import 'package:flutter_demo/features/internship/domain/entities/free_internship_exempt.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_approval/free_internship_approval_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_approval/free_internship_approval_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/free_internship_approval/free_internship_approval_state.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/student_header.dart';

/// 免实习申请详情页面
///
/// 用于查看和审批免实习申请的详情页面，支持通过和驳回操作
class FreeInternshipApprovalDetailScreen extends StatefulWidget {
  const FreeInternshipApprovalDetailScreen({
    required this.exempt,
    super.key,
  });

  final FreeInternshipExempt exempt;

  @override
  State<FreeInternshipApprovalDetailScreen> createState() =>
      _FreeInternshipApprovalDetailScreenState();
}

class _FreeInternshipApprovalDetailScreenState
    extends State<FreeInternshipApprovalDetailScreen> with PageLoggingMixin {
  late FreeInternshipApprovalBloc _approvalBloc;
  late FreeInternshipExempt _exempt;

  @override
  void initState() {
    super.initState();
    logPageEntry(); // 记录页面进入日志

    _approvalBloc = GetIt.instance<FreeInternshipApprovalBloc>();
    _exempt = widget.exempt;
  }

  @override
  void dispose() {
    _approvalBloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _approvalBloc,
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        appBar: CustomAppBar(
          title: '免实习申请详情',
          actions: [
            IconButton(
              icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
              onPressed: () {
                // TODO(feature): 添加统计功能
              },
            ),
          ],
        ),
        body: BlocConsumer<FreeInternshipApprovalBloc, FreeInternshipApprovalState>(
          listener: (context, state) {
            if (state is FreeInternshipApprovalSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              // 返回上一页并传递刷新标志
              Navigator.of(context).pop(true);
            } else if (state is FreeInternshipApprovalError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            return Stack(
              children: [
                SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 10),

                      // 学生信息头部
                      StudentHeader(
                        name: _exempt.studentName,
                        avatar: _exempt.avatar,
                      ),

                      const SizedBox(height: 10),

                      // 免实习申请信息
                      _buildApplicationInfo(),

                      // 审批状态
                      _buildApprovalStatus(),

                      // 底部空间，避免按钮遮挡
                      const SizedBox(height: 80),
                    ],
                  ),
                ),

                // 加载状态遮罩
                if (state is FreeInternshipApprovalLoading)
                  Container(
                    color: Colors.black.withValues(alpha: 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
              ],
            );
          },
        ),
        // 底部按钮
        bottomNavigationBar: BlocBuilder<FreeInternshipApprovalBloc, FreeInternshipApprovalState>(
          builder: (context, state) {
            final isLoading = state is FreeInternshipApprovalLoading;
            return _buildBottomBar(isLoading: isLoading) ?? const SizedBox.shrink();
          },
        ),
      ),
    );
  }



  // 免实习申请信息
  Widget _buildApplicationInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Image.asset('assets/images/job_information_icon.png', width: 25.w, height: 28.h),
              const SizedBox(width: 8),
              Text(
                '免实习申请信息',
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
          const SizedBox(height: 16),

          // 免实习理由
          _buildInfoRow('免实习理由', _exempt.reason),

          const SizedBox(height: 16),

          // 证明文件
          _buildAttachmentRow('证明文件', _exempt.fileUrl),
        ],
      ),
    );
  }

  // 审批状态
  Widget _buildApprovalStatus() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Image.asset('assets/images/internship_information_icon.png', width: 25.w, height: 28.h),
              const SizedBox(width: 8),
              Text(
                '审批状态',
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
          const SizedBox(height: 16),

          // 审批人信息
          Row(
            children: [
              // 头像
              UserAvatar.small(
                imageUrl: 'assets/images/teacher_avatar.png',
                type: AvatarType.asset,
                userName: '冯项老师',
              ),
              const SizedBox(width: 12),
              // 审批人信息
              const Text(
                '冯项老师 (班主任)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              // 审批状态
              _buildApprovalStatusTag(_exempt.statusText),
            ],
          ),

          // 如果是已通过状态，显示驳回按钮
          if (_exempt.isApproved) ...[
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                width: 120,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: TextButton(
                  onPressed: _showRejectDialog,
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.red,
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '驳回',
                    style: TextStyle(fontSize: 16,fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 底部导航栏
  Widget? _buildBottomBar({bool isLoading = false}) {
    // 根据状态显示不同的底部按钮
    if (_exempt.isPending) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 驳回按钮
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading ? null : _showRejectDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '驳回',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // 通过按钮
            Expanded(
              child: ElevatedButton(
                onPressed: isLoading ? null : _showApproveDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '通过',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 已通过或已驳回状态，不显示底部按钮
      return null;
    }
  }

  // 信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 附件行
  Widget _buildAttachmentRow(String label, String fileUrl) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: fileUrl.isNotEmpty
              ? Stack(
                  children: [
                    // 附件图片
                    InkWell(
                      onTap: () => _viewAttachment(fileUrl),
                      child: Container(
                        width: 98,
                        height: 98,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        clipBehavior: Clip.antiAlias,
                        child: fileUrl.startsWith('http')
                            ? Image.network(
                                fileUrl,
                                width: 98,
                                height: 98,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) => _buildFileIcon(),
                              )
                            : _buildFileIcon(),
                      ),
                    ),

                    // 状态图标
                    if (_exempt.status != 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Image.asset(
                          _exempt.status == 1
                              ? 'assets/images/passed_icon.png'
                              : 'assets/images/rejected_icon.png',
                          width: 60,
                          height: 60,
                        ),
                      ),
                  ],
                )
              : const Text(
                  '无',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
        ),
      ],
    );
  }

  // 审批状态标签
  Widget _buildApprovalStatusTag(String status) {
    Color bgColor;
    Color textColor;
    IconData? icon;

    switch (status) {
      case '待审批':
        bgColor = Colors.blue.withValues(alpha: 0.1);
        textColor = AppTheme.primaryColor;
        icon = null;
        break;
      case '已通过':
        bgColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case '已驳回':
        bgColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        icon = Icons.cancel;
        break;
      default:
        bgColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        icon = null;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 16, color: textColor),
            const SizedBox(width: 4),
          ],
          Text(
            status,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 构建文件图标
  Widget _buildFileIcon() {
    return Container(
      width: 98,
      height: 98,
      color: Colors.grey[200],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.insert_drive_file,
              size: 36,
              color: Colors.grey,
            ),
            SizedBox(height: 4),
            Text(
              '证明文件',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.primaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // 查看附件
  void _viewAttachment(String fileUrl) {
    if (fileUrl.isEmpty) {
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewerScreen(
          imageUrl: fileUrl,
          title: '证明文件',
          isNetworkImage: fileUrl.startsWith('http'),
        ),
      ),
    );
  }

  // 显示通过确认对话框
  void _showApproveDialog() {
    final TextEditingController opinionController = TextEditingController();
    opinionController.text = '审批通过'; // 默认审批意见

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认通过'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('确定要通过该免实习申请吗？'),
            const SizedBox(height: 16),
            const Text('审批意见：'),
            const SizedBox(height: 8),
            TextField(
              controller: opinionController,
              decoration: const InputDecoration(
                hintText: '请输入审批意见',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _approveApplication(opinionController.text.trim());
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 显示驳回确认对话框
  void _showRejectDialog() {
    final TextEditingController opinionController = TextEditingController();
    opinionController.text = '审批驳回'; // 默认审批意见

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认驳回'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('确定要驳回该免实习申请吗？'),
            const SizedBox(height: 16),
            const Text('驳回理由：'),
            const SizedBox(height: 8),
            TextField(
              controller: opinionController,
              decoration: const InputDecoration(
                hintText: '请输入驳回理由',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _rejectApplication(opinionController.text.trim());
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 通过申请
  void _approveApplication(String reviewOpinion) {
    final String finalOpinion = reviewOpinion.isEmpty ? '审批通过' : reviewOpinion;

    // 触发BLoC事件
    _approvalBloc.add(ApproveFreeInternshipEvent(
      id: _exempt.id,
      reviewOpinion: finalOpinion,
      isApproved: true,
    ));
  }

  // 驳回申请
  void _rejectApplication(String reviewOpinion) {
    final String finalOpinion = reviewOpinion.isEmpty ? '审批驳回' : reviewOpinion;

    // 触发BLoC事件
    _approvalBloc.add(ApproveFreeInternshipEvent(
      id: _exempt.id,
      reviewOpinion: finalOpinion,
      isApproved: false,
    ));
  }
}
