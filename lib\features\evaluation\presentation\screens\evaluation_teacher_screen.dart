/// -----
/// evaluation_teacher_screen.dart
/// 
/// 评价老师页面，提供学生选择不同类型老师进行评价的功能。
///
/// <AUTHOR>
/// @date 2025-05-22
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 评价老师页面
/// 
/// 允许学生选择不同类型的老师进行评价。
/// 主要功能：
/// - 显示不同类型的老师列表
/// - 点击列表项进入对应老师的评价页面
class EvaluationTeacherScreen extends StatefulWidget {
  /// 创建评价老师页面
  const EvaluationTeacherScreen({Key? key}) : super(key: key);

  @override
  State<EvaluationTeacherScreen> createState() => _EvaluationTeacherScreenState();
}

class _EvaluationTeacherScreenState extends State<EvaluationTeacherScreen> {
  /// 老师类型列表
  final List<TeacherType> _teacherTypes = [
    TeacherType(
      id: '1',
      title: '评价班主任',
      avatar: 'assets/images/teacher_avatar1.png',
      color: const Color(0xFFB3D4FC),
    ),
    TeacherType(
      id: '2',
      title: '评价校内指导老师',
      avatar: 'assets/images/teacher_avatar2.png',
      color: const Color(0xFF8CD0E9),
    ),
    TeacherType(
      id: '3',
      title: '评价企业指导老师',
      avatar: 'assets/images/teacher_avatar3.png',
      color: const Color(0xFFF8D48A),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '评价老师',
        backgroundColor: Colors.white,
      ),
      body: ListView.builder(
        itemCount: _teacherTypes.length,
        itemBuilder: (context, index) {
          return TeacherTypeItem(
            teacherType: _teacherTypes[index],
            onTap: () => _navigateToTeacherEvaluation(_teacherTypes[index]),
          );
        },
      ),
    );
  }
  
  /// 导航到老师评价详情页面
  void _navigateToTeacherEvaluation(TeacherType teacherType) {
    AppSnackBar.showSuccess(context, '评价${teacherType.title}');
  }
}

/// 老师类型数据模型
class TeacherType {
  final String id;
  final String title;
  final String avatar;
  final Color color;

  TeacherType({
    required this.id,
    required this.title,
    required this.avatar,
    required this.color,
  });
}

/// 老师类型列表项组件
class TeacherTypeItem extends StatelessWidget {
  final TeacherType teacherType;
  final VoidCallback onTap;

  const TeacherTypeItem({
    Key? key,
    required this.teacherType,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(top: 16, left: 16,right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end, // 使内容靠右对齐
          children: [
            // 左侧头像
            Container(
              margin: const EdgeInsets.all(16),
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: teacherType.color,
                shape: BoxShape.circle,
              ),
              child: ClipOval(
                child: Image.asset(
                  width: 80.w,
                  height: 80.h,
                  teacherType.avatar,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.white,
                    );
                  },
                ),
              ),
            ),
            // 标题和箭头容器
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end, // 使内容靠右对齐
                children: [
                  // 标题
                  Text(
                    teacherType.title,
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.black333,
                    ),
                  ),
                  // 右侧箭头
                  const Padding(
                    padding: EdgeInsets.only(left: 8, right: 16),
                    child: Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
