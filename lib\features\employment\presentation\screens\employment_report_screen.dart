/// -----
/// employment_report_screen.dart
/// 
/// 就业上报页面，用于学生提交就业信息，包括就业类别、协议类别、企业信息等
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';
import 'package:flutter_demo/core/widgets/file_upload_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 就业上报页面
class EmploymentReportScreen extends StatefulWidget {
  const EmploymentReportScreen({super.key});

  @override
  State<EmploymentReportScreen> createState() => _EmploymentReportScreenState();
}

class _EmploymentReportScreenState extends State<EmploymentReportScreen> {
  // 就业类别选项
  final List<String> _employmentTypes = ['签就业协议形式就业', '签劳动合同形式就业', '其他录用形式就业', '升学', '自主创业', '自由职业', '不就业'];
  String _selectedEmploymentType = '签就业协议形式就业';

  // 协议类别选项
  final List<String> _agreementTypes = ['三方协议', '就业协议', '劳动合同', '意向书', '其他'];
  String _selectedAgreementType = '三方协议';

  // 企业信息
  String _companyName = '';
  String _creditCode = '';
  String _companySize = '50-200人';
  String _companyType = '民营企业';
  String _industry = 'IT/互联网';
  String _location = '';
  String _address = '';
  String _contactPerson = '';
  String _contactPhone = '';
  String _email = '';
  String _zipCode = '';

  // 岗位信息
  String _department = '';
  String _positionName = '';
  String _positionType = '技术类';
  String _professionalMatch = '基本匹配';
  String _salary = '';
  String _contractPeriod = '';
  String _workContent = '';

  // 附件文件
  File? _agreementFile;

  // 图片选择器
  final ImagePicker _picker = ImagePicker();

  // 企业规模选项
  final List<String> _companySizes = ['50人以下', '50-200人', '200-500人', '500-1000人', '1000人以上'];
  
  // 企业性质选项
  final List<String> _companyTypes = ['国有企业', '民营企业', '外资企业', '合资企业', '事业单位', '其他'];
  
  // 所属行业选项
  final List<String> _industries = ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '其他'];
  
  // 岗位类别选项
  final List<String> _positionTypes = ['管理人员', '技术人员', '市场营销人员', '财务人员', '行政人员'];
  
  // 专业匹配选项
  final List<String> _professionMatches = ['完全匹配', '基本匹配', '不匹配但接受'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '就业上报',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                // 企业信息部分
                _buildCompanyInfoSection(),

                // 岗位信息部分
                _buildPositionInfoSection(),

                // 附件上传部分
                _buildAttachmentsSection(),
              ],
            ),
          ),

          // 提交按钮
          _buildSubmitButton(),
        ],
      ),
    );
  }

  /// 构建企业信息部分
  Widget _buildCompanyInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '企业信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),
        // 就业类别
        FormFieldItem(
          label: '就业类别',
          value: _selectedEmploymentType,
          type: FormFieldType.dropdown,
          dropdownItems: _employmentTypes,
          onTap: () => _showDropdownDialog('就业类别', _employmentTypes, (value) {
            setState(() {
              _selectedEmploymentType = value;
            });
          }),
        ),

        // 协议类别
        FormFieldItem(
          label: '协议类别',
          value: _selectedAgreementType,
          type: FormFieldType.dropdown,
          dropdownItems: _agreementTypes,
          onTap: () => _showDropdownDialog('协议类别', _agreementTypes, (value) {
            setState(() {
              _selectedAgreementType = value;
            });
          }),
        ),
        // 企业名称
        FormFieldItem(
          label: '企业名称',
          value: _companyName,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _companyName = value;
            });
          },
        ),

        // 统一社会信用代码
        FormFieldItem(
          label: '统一社会信用代码',
          value: _creditCode,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _creditCode = value;
            });
          },
        ),

        // 企业规模
        FormFieldItem(
          label: '企业规模',
          value: _companySize,
          type: FormFieldType.dropdown,
          dropdownItems: _companySizes,
          onTap: () => _showDropdownDialog('企业规模', _companySizes, (value) {
            setState(() {
              _companySize = value;
            });
          }),
        ),

        // 企业性质
        FormFieldItem(
          label: '企业性质',
          value: _companyType,
          type: FormFieldType.dropdown,
          dropdownItems: _companyTypes,
          onTap: () => _showDropdownDialog('企业性质', _companyTypes, (value) {
            setState(() {
              _companyType = value;
            });
          }),
        ),

        // 所属行业
        FormFieldItem(
          label: '所属行业',
          value: _industry,
          type: FormFieldType.dropdown,
          dropdownItems: _industries,
          onTap: () => _showDropdownDialog('所属行业', _industries, (value) {
            setState(() {
              _industry = value;
            });
          }),
        ),

        // 企业所在地
        FormFieldItem(
          label: '企业所在地',
          value: _location,
          type: FormFieldType.location,
          onTap: () {
            AppSnackBar.showWarning(context, '地址选择功能待实现');
          },
        ),

        // 详细地址
        FormFieldItem(
          label: '详细地址',
          value: _address,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _address = value;
            });
          },
        ),

        // 联系人
        FormFieldItem(
          label: '企业联系人',
          value: _contactPerson,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _contactPerson = value;
            });
          },
        ),

        // 联系人电话
        FormFieldItem(
          label: '企业联系人电话',
          value: _contactPhone,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _contactPhone = value;
            });
          },
        ),

        // 电子邮箱
        FormFieldItem(
          label: '企业邮箱',
          value: _email,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _email = value;
            });
          },
        ),

        // 邮政编码
        FormFieldItem(
          label: '邮政编码',
          value: _zipCode,
          type: FormFieldType.input,
          showDivider: false,
          onChanged: (value) {
            setState(() {
              _zipCode = value;
            });
          },
        ),
      ],
    );
  }

  /// 构建岗位信息部分
  Widget _buildPositionInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '岗位信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 部门
        FormFieldItem(
          label: '部门',
          value: _department,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _department = value;
            });
          },
        ),

        // 岗位名称
        FormFieldItem(
          label: '岗位名称',
          value: _positionName,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _positionName = value;
            });
          },
        ),

        // 岗位类别
        FormFieldItem(
          label: '岗位类别',
          value: _positionType,
          type: FormFieldType.dropdown,
          dropdownItems: _positionTypes,
          onTap: () => _showDropdownDialog('岗位类别', _positionTypes, (value) {
            setState(() {
              _positionType = value;
            });
          }),
        ),

        // 专业匹配度
        FormFieldItem(
          label: '专业匹配度',
          value: _professionalMatch,
          type: FormFieldType.dropdown,
          dropdownItems: _professionMatches,
          onTap: () => _showDropdownDialog('专业匹配度', _professionMatches, (value) {
            setState(() {
              _professionalMatch = value;
            });
          }),
        ),

        // 就业薪酬
        FormFieldItem(
          label: '就业薪酬',
          value: _salary,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _salary = value;
            });
          },
        ),

        // 合同期限
        FormFieldItem(
          label: '合同期限',
          value: _contractPeriod,
          type: FormFieldType.input,
          onChanged: (value) {
            setState(() {
              _contractPeriod = value;
            });
          },
        ),

        // 工作内容
        FormFieldItem(
          label: '工作内容',
          value: _workContent,
          type: FormFieldType.input,
          showDivider: false,
          onChanged: (value) {
            setState(() {
              _workContent = value;
            });
          },
        ),
      ],
    );
  }

  /// 构建附件上传部分
  Widget _buildAttachmentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 协议照片上传
        FileUploadItem(
          title: '附件照片',
          file: _agreementFile,
          onTap: () => _pickFile(),
        ),

        SizedBox(height: 20.h),
      ],
    );
  }

  /// 构建提交按钮
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: ElevatedButton(
        onPressed: _handleSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: EdgeInsets.symmetric(vertical: 24.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '修改',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// 处理提交操作
  void _handleSubmit() {
    // 验证表单
    if (_validateForm()) {
      AppSnackBar.showSuccess(context, '提交成功');
    }
  }

  /// 验证表单
  bool _validateForm() {
    // 企业信息验证
    if (_companyName.isEmpty) {
      AppSnackBar.showError(context, '请输入企业名称');
      return false;
    }

    // 岗位信息验证
    if (_positionName.isEmpty) {
      AppSnackBar.showError(context, '请输入岗位名称');
      return false;
    }

    // 薪资验证
    if (_salary.isEmpty) {
      AppSnackBar.showError(context, '请输入就业薪酬');
      return false;
    }

    return true;
  }

  /// 选择文件
  Future<void> _pickFile() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile != null) {
        setState(() {
          _agreementFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      AppSnackBar.showError(context, '文件选择失败: $e');
    }
  }

  /// 显示下拉选择对话框（底部弹出式）
  void _showDropdownDialog(
      String title, List<String> items, Function(String) onSelected) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Center(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ),
          ),
          
          // 选项列表
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      title: Center(
                        child: Text(
                          items[index],
                          style: TextStyle(
                            fontSize: 30.sp,
                            color: AppTheme.black333,
                          ),
                        ),
                      ),
                      onTap: () {
                        onSelected(items[index]);
                        Navigator.pop(context);
                      },
                    ),
                    Divider(height: 1.h, thickness: 1.h, color: AppTheme.dividerColor),
                  ],
                );
              },
            ),
          ),
          
          // 取消按钮
          InkWell(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
