/// -----
/// report_content_widget.dart
/// 
/// 报告内容编辑组件
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';

class ReportContentWidget extends StatelessWidget {
  final ReportType reportType;
  final TextEditingController controller;
  final bool hasAIContent;
  final bool isGeneratingAI;
  final Function(String) onContentChanged;
  final VoidCallback? onGenerateAI;

  const ReportContentWidget({
    Key? key,
    required this.reportType,
    required this.controller,
    required this.hasAIContent,
    required this.isGeneratingAI,
    required this.onContentChanged,
    this.onGenerateAI,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 25.w,vertical: 40.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和AI提示
          _buildHeader(),

          SizedBox(height: 24.h),

          // 日报的引导问题
          if (reportType == ReportType.daily) ...[
            _buildDailyGuideQuestions(),
            SizedBox(height: 24.h),
          ],

          // AI提示（非日报）
          if (reportType != ReportType.daily && hasAIContent) ...[
            _buildAITip(),
            SizedBox(height: 24.h),
          ],

          // 内容输入框
          _buildContentInput(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          _getContentTitle(),
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        const Spacer(),
        if (reportType != ReportType.daily && onGenerateAI != null)
          _buildAIGenerateButton(),
      ],
    );
  }

  Widget _buildAIGenerateButton() {
    return GestureDetector(
      onTap: isGeneratingAI ? null : onGenerateAI,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isGeneratingAI 
              ? const Color(0xFFF5F5F5)
              : const Color(0xFF2165F6).withOpacity(0.1),
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color: isGeneratingAI 
                ? const Color(0xFFE5E5E5)
                : const Color(0xFF2165F6),
            width: 1.w,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isGeneratingAI) ...[
              SizedBox(
                width: 24.w,
                height: 24.w,
                child: const CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Color(0xFF666666),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '生成中...',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: const Color(0xFF666666),
                ),
              ),
            ] else ...[
              Icon(
                Icons.auto_awesome,
                size: 24.w,
                color: const Color(0xFF2165F6),
              ),
              SizedBox(width: 8.w),
              Text(
                'AI生成',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: const Color(0xFF2165F6),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDailyGuideQuestions() {
    const questions = [
      '1.今天的主要工作是什么？',
      '2.您在工作中有哪些思考？',
      '3.您觉得哪些可以继续优化？',
      '4.您的收获是什么？',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '您可以从以下几个方面来描述您的日报内容：',
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF666666),
          ),
        ),
        SizedBox(height: 16.h),
        ...questions.map((question) => Padding(
          padding: EdgeInsets.only(bottom: 8.h),
          child: Text(
            question,
            style: TextStyle(
              fontSize: 28.sp,
              color: const Color(0xFF666666),
            ),
          ),
        )).toList(),
      ],
    );
  }

  Widget _buildAITip() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 32.w,
            color: const Color(0xFF2165F6),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              _getAITipText(),
              style: TextStyle(
                fontSize: 26.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentInput() {
    return Container(
      height: 300.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: const Color(0xFFE5E5E5),
          width: 1.w,
        ),
      ),
      child: TextField(
        controller: controller,
        onChanged: onContentChanged,
        maxLines: 12,
        style: TextStyle(
          fontSize: 24.sp,
          color: const Color(0xFF333333),
          height: 1.5,
        ),
        decoration: InputDecoration(
          hintText: _getHintText(),
          hintStyle: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFFCCCCCC),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(24.w),
        ),
      ),
    );
  }

  String _getContentTitle() {
    switch (reportType) {
      case ReportType.daily:
        return '日报内容';
      case ReportType.weekly:
        return '周报内容';
      case ReportType.monthly:
        return '月报内容';
      case ReportType.summary:
        return '总结内容';
    }
  }

  String _getAITipText() {
    switch (reportType) {
      case ReportType.weekly:
        return 'AI已根据您本周的日报，自动生成周报，详情如下。';
      case ReportType.monthly:
        return 'AI已根据您本月的报告，自动生成月报，详情如下。';
      case ReportType.summary:
        return 'AI已根据您的实习记录，自动生成总结，详情如下。';
      default:
        return '';
    }
  }

  String _getHintText() {
    switch (reportType) {
      case ReportType.daily:
        return '请按以上内容描述你的日报';
      case ReportType.weekly:
        return '您可以在此基础上进行修改和完善...';
      case ReportType.monthly:
        return '您可以在此基础上进行修改和完善...';
      case ReportType.summary:
        return '您可以在此基础上进行修改和完善...';
    }
  }
}
