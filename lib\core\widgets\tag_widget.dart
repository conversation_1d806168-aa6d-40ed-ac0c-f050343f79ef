
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class TagWidget extends StatelessWidget {
  final String tag;
  final Color backgroundColor;
  final Color textColor;
  const TagWidget({
    super.key,
    required this.tag,
    this.backgroundColor = const Color(0xFFFF4747),
    this.textColor = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 20.sp,
          color: textColor,
        ),
      ),
    );
  }

}
