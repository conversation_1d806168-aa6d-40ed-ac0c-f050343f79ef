/// -----
/// progress_ring_examples.dart
/// 
/// 圆环进度组件使用示例，展示各种使用方式
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'progress_ring.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 圆环进度组件使用示例页面
class ProgressRingExamplesPage extends StatefulWidget {
  const ProgressRingExamplesPage({Key? key}) : super(key: key);

  @override
  _ProgressRingExamplesPageState createState() => _ProgressRingExamplesPageState();
}

class _ProgressRingExamplesPageState extends State<ProgressRingExamplesPage> {
  double _progress = 0.75;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('圆环进度组件示例'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 进度控制
            _buildProgressControl(),
            
            SizedBox(height: 30.h),
            
            // 基础圆环组件示例
            _buildSectionTitle('基础圆环组件'),
            _buildBasicRingExamples(),
            
            SizedBox(height: 30.h),
            
            // 内容显示组件示例
            _buildSectionTitle('内容显示组件'),
            _buildContentExamples(),
            
            SizedBox(height: 30.h),
            
            // 统一圆环组件示例
            _buildSectionTitle('统一圆环组件'),
            _buildUnifiedRingExamples(),
            
            SizedBox(height: 30.h),
            
            // 预定义样式示例
            _buildSectionTitle('预定义样式'),
            _buildPresetStyleExamples(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressControl() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '进度控制: ${(_progress * 100).round()}%',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.h),
            Slider(
              value: _progress,
              onChanged: (value) {
                setState(() {
                  _progress = value;
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 15.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildBasicRingExamples() {
    return Wrap(
      spacing: 20.w,
      runSpacing: 20.h,
      children: [
        // 基础圆环 - 默认样式
        BaseProgressRing(
          progress: _progress,
          size: 100,
        ),
        
        // 基础圆环 - 自定义颜色
        BaseProgressRing(
          progress: _progress,
          size: 100,
          progressColor: Colors.green,
          backgroundColor: Colors.green.withOpacity(0.2),
        ),
        
        // 基础圆环 - 带动画
        BaseProgressRing(
          progress: _progress,
          size: 100,
          progressColor: Colors.orange,
          enableAnimation: true,
        ),
      ],
    );
  }

  Widget _buildContentExamples() {
    return Wrap(
      spacing: 20.w,
      runSpacing: 20.h,
      children: [
        // 分数样式内容
        Container(
          width: 120.w,
          height: 120.w,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Center(
            child: ProgressRingContent(
              mainText: '85',
              subtitle: '总评分',
              mainTextStyle: ProgressRingContentStyles.scoreMainTextStyle,
              subtitleStyle: ProgressRingContentStyles.standardSubtitleStyle,
            ),
          ),
        ),
        
        // 计数样式内容
        Container(
          width: 120.w,
          height: 120.w,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Center(
            child: ProgressRingContent(
              mainText: '15/30',
              subtitle: '签到天数',
              mainTextStyle: ProgressRingContentStyles.countMainTextStyle,
              subtitleStyle: ProgressRingContentStyles.smallSubtitleStyle,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUnifiedRingExamples() {
    return Wrap(
      spacing: 20.w,
      runSpacing: 20.h,
      children: [
        // 统一圆环 - 默认样式
        UnifiedProgressRing(
          progress: _progress,
          mainText: '${(_progress * 100).round()}',
          subtitle: '完成度',
          size: 120,
        ),
        
        // 统一圆环 - 带动画
        UnifiedProgressRing(
          progress: _progress,
          mainText: '${(_progress * 100).round()}%',
          subtitle: '进度',
          size: 120,
          progressColor: Colors.purple,
          enableAnimation: true,
        ),
      ],
    );
  }

  Widget _buildPresetStyleExamples() {
    return Wrap(
      spacing: 20.w,
      runSpacing: 20.h,
      children: [
        // 评分样式
        UnifiedProgressRingStyles.score(
          progress: _progress,
          score: '${(_progress * 100).round()}',
          title: '校内老师总评分',
          enableAnimation: true,
        ),
        
        // 签到样式
        UnifiedProgressRingStyles.signIn(
          progress: _progress,
          signedDays: (_progress * 30).round(),
          totalDays: 30,
          label: '签到天数',
          enableAnimation: true,
        ),
        
        // 百分比样式
        UnifiedProgressRingStyles.percentage(
          progress: _progress,
          title: '完成率',
          enableAnimation: true,
        ),
        
        // 计数样式
        UnifiedProgressRingStyles.count(
          progress: _progress,
          count: '${(_progress * 50).round()}',
          title: '任务完成',
          enableAnimation: true,
        ),
      ],
    );
  }
}

/// 圆环组件使用指南
class ProgressRingUsageGuide {
  /// 基础使用示例
  static Widget basicUsage() {
    return const BaseProgressRing(
      progress: 0.75,
      size: 120,
      progressColor: Color(0xFF2979FF),
    );
  }

  /// 内容显示示例
  static Widget contentUsage() {
    return const ProgressRingContent(
      mainText: '85',
      subtitle: '总评分',
    );
  }

  /// 完整圆环示例
  static Widget completeUsage() {
    return const UnifiedProgressRing(
      progress: 0.85,
      mainText: '85',
      subtitle: '校内老师总评分',
      size: 160,
      enableAnimation: true,
    );
  }

  /// 预定义样式示例
  static Widget presetUsage() {
    return UnifiedProgressRingStyles.score(
      progress: 0.85,
      score: '85',
      title: '校内老师总评分',
      enableAnimation: true,
    );
  }
}
