/// 修复响应式适配导入的脚本
/// 
/// 使用方法：
/// dart run scripts/fix_responsive_imports.dart

import 'dart:io';

void main() async {
  print('开始修复响应式适配导入...');
  
  const responsiveImport = "import 'package:flutter_demo/core/utils/responsive_util.dart';";
  
  // 扫描lib目录下的所有dart文件
  final libDir = Directory('lib');
  if (!libDir.existsSync()) {
    print('错误：lib目录不存在');
    return;
  }
  
  int fixedFiles = 0;
  
  await for (final entity in libDir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final content = await entity.readAsString();
      
      // 检查是否使用了响应式扩展方法但没有导入
      final usesResponsive = _usesResponsiveExtensions(content);
      final hasImport = content.contains(responsiveImport);
      
      if (usesResponsive && !hasImport) {
        // 需要添加导入
        final newContent = _addResponsiveImport(content, responsiveImport);
        await entity.writeAsString(newContent);
        
        fixedFiles++;
        print('✓ 已修复: ${entity.path}');
      }
    }
  }
  
  print('\n修复完成！');
  print('共修复文件: $fixedFiles');
  
  if (fixedFiles > 0) {
    print('\n请运行以下命令重新编译：');
    print('flutter build web --debug');
  }
}

/// 检查是否使用了响应式扩展方法
bool _usesResponsiveExtensions(String content) {
  // 检查是否包含 .w、.h、.sp、.r 等扩展方法
  final patterns = [
    RegExp(r'\d+\.w\b'),
    RegExp(r'\d+\.h\b'),
    RegExp(r'\d+\.sp\b'),
    RegExp(r'\d+\.r\b'),
  ];
  
  return patterns.any((pattern) => pattern.hasMatch(content));
}

/// 添加响应式导入
String _addResponsiveImport(String content, String importStatement) {
  final lines = content.split('\n');
  
  // 找到最后一个import语句的位置
  int lastImportIndex = -1;
  for (int i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ')) {
      lastImportIndex = i;
    }
  }
  
  if (lastImportIndex >= 0) {
    // 在最后一个import语句后添加新的import
    lines.insert(lastImportIndex + 1, importStatement);
  } else {
    // 如果没有找到import语句，在文件开头添加
    // 跳过注释和空行
    int insertIndex = 0;
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty || line.startsWith('//') || line.startsWith('/*') || line.startsWith('*') || line.startsWith('*/')) {
        insertIndex = i + 1;
      } else {
        break;
      }
    }
    lines.insert(insertIndex, importStatement);
  }
  
  return lines.join('\n');
}
