/// -----
/// file_upload_item.dart
/// 
/// 通用文件上传组件，用于显示文件上传区域和预览已上传的文件
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 文件上传组件
///
/// 提供统一的文件上传界面，包括上传前的占位区域和上传后的预览效果
class FileUploadItem extends StatelessWidget {
  /// 文件标题
  final String title;
  
  /// 已上传的文件
  final File? file;
  
  /// 点击上传回调
  final VoidCallback onTap;
  
  /// 构造函数
  const FileUploadItem({
    Key? key,
    required this.title,
    this.file,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题部分
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 19.h),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
        ),

        // 上传区域
        Container(
          color: Colors.white,
          width: double.infinity,
          height: 260.h,
          child: Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: onTap,
              child: Container(
                width: 120,
                height: 120,
                margin: EdgeInsets.only(left: 27.w),
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: file != null
                    ? Stack(
                        alignment: Alignment.center,
                        children: [
                          // 显示已上传的图片
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4.r),
                            child: Image.file(
                              file!,
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                            ),
                          ),
                          // 重新上传按钮
                          Positioned(
                            right: 8,
                            top: 8,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.refresh,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      )
                    : const Center(
                        child: Icon(
                          Icons.add,
                          size: 40,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
