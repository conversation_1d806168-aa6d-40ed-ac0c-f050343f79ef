/// -----------------------------------------------------------------------------
/// report_statistics_screen.dart
///
/// 学生周报报统计页面，用于展示学生周报提交统计数据和学生列表
///
/// 功能：
/// 1. 展示周报提交统计数据（未提交、已提交、已批阅、待批阅）
/// 2. 提供未提交/已提交的分类查看
/// 3. 展示学生列表及其状态，支持提醒功能
/// 4. 支持时间范围选择
///
/// 使用方法：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(builder: (context) => const ReportStatisticsScreen()),
/// );
/// ```
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/sticky_tab_bar.dart';
import 'package:flutter_demo/core/widgets/student_list_item.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 周次数据模型
class WeekModel {
  final int weekNumber;
  final String startDate;
  final String endDate;
  final String displayText;

  WeekModel({
    required this.weekNumber,
    required this.startDate,
    required this.endDate,
    required this.displayText,
  });

  /// 获取周次范围显示文本
  String get dateRange => '$startDate-$endDate';

  /// 获取周次标题
  String get weekTitle => '第${weekNumber}周';

  /// 生成示例周次数据
  static List<WeekModel> generateSampleWeeks() {
    final List<WeekModel> weeks = [];
    final DateTime startDate = DateTime(2023, 3, 6); // 学期开始日期

    for (int i = 1; i <= 20; i++) {
      final DateTime weekStart = startDate.add(Duration(days: (i - 1) * 7));
      final DateTime weekEnd = weekStart.add(const Duration(days: 6));

      weeks.add(WeekModel(
        weekNumber: i,
        startDate: '${weekStart.year}.${weekStart.month.toString().padLeft(2, '0')}.${weekStart.day.toString().padLeft(2, '0')}',
        endDate: '${weekEnd.year}.${weekEnd.month.toString().padLeft(2, '0')}.${weekEnd.day.toString().padLeft(2, '0')}',
        displayText: i == 3 ? '本周' : '第${i}周',
      ));
    }

    return weeks;
  }
}

/// 报告学生数据模型
class ReportStudentModel implements StudentInfo {
  @override
  final String id;
  @override
  final String name;
  @override
  final String phone;
  @override
  final String avatar;
  @override
  final String status; // '待实习', '实习中'
  final bool hasSubmitted; // 是否已提交报告

  ReportStudentModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.avatar,
    required this.status,
    required this.hasSubmitted,
  });

  /// 获取示例数据
  static List<ReportStudentModel> getSampleData() {
    return [
      ReportStudentModel(
        id: '1',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '待实习',
        hasSubmitted: false,
      ),
      ReportStudentModel(
        id: '2',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasSubmitted: false,
      ),
      ReportStudentModel(
        id: '3',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasSubmitted: false,
      ),
      ReportStudentModel(
        id: '4',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasSubmitted: false,
      ),
      ReportStudentModel(
        id: '5',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasSubmitted: true,
      ),
    ];
  }
}

/// 学生周报报统计页面
class ReportStatisticsScreen extends StatefulWidget {
  const ReportStatisticsScreen({Key? key}) : super(key: key);

  @override
  State<ReportStatisticsScreen> createState() => _ReportStatisticsScreenState();
}

class _ReportStatisticsScreenState extends State<ReportStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';

  // 周次相关状态
  late List<WeekModel> _weeks;
  late WeekModel _selectedWeek;
  String get _currentWeek => _selectedWeek.displayText;

  late List<ReportStudentModel> _allStudents;
  late List<ReportStudentModel> _unsubmittedStudents;
  late List<ReportStudentModel> _submittedStudents;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载数据
  void _loadData() {
    // 初始化周次数据
    _weeks = WeekModel.generateSampleWeeks();
    _selectedWeek = _weeks[2]; // 默认选择第3周（本周）

    _allStudents = ReportStudentModel.getSampleData();
    // 模拟数据分类
    _unsubmittedStudents = _allStudents.where((s) => !s.hasSubmitted).toList();
    _submittedStudents = _allStudents.where((s) => s.hasSubmitted).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: CustomAppBar(
        title: '学生周报报统计',
        actions: [
          // 时间选择器
          Container(
            margin: EdgeInsets.only(right: 32.w),
            child: GestureDetector(
              onTap: _showWeekSelector,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _currentWeek,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: AppTheme.black333,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    size: 32.w,
                    color: AppTheme.black666,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // 课程头部和统计数据
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // 课程头部
                  CourseHeaderSection(
                    courseName: _courseName,
                  ),
                  // 第一条分割线：课程头部和统计数据之间
                  Container(
                    height: 1.h,
                    color: AppTheme.dividerColor,
                    margin: EdgeInsets.symmetric(horizontal: 40.w),
                  ),
                  // 统计数据区域
                  _buildStatisticsSection(),
                  // 第二条分割线：统计数据和Tab栏之间
                  Container(
                    height: 1.h,
                    color: AppTheme.dividerColor,
                    margin: EdgeInsets.symmetric(horizontal: 40.w),
                  ),
                ],
              ),
            ),
            // 固定的Tab栏
            SliverPersistentHeader(
              pinned: true,
              delegate: StickyTabBarDelegate(
                tabBar: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.blue2165f6,
                  unselectedLabelColor: AppTheme.black666,
                  indicatorColor: AppTheme.blue2165f6,
                  indicatorWeight: 3,
                  labelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  tabs: [
                    CountedTab(
                      text: '未提交',
                      count: _unsubmittedStudents.length,
                      showCountAsBadge: true,
                    ),
                    const Tab(text: '已提交'),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildStudentList(_unsubmittedStudents),
            _buildStudentList(_submittedStudents),
          ],
        ),
      ),
    );
  }

  /// 显示周次选择器
  void _showWeekSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => _buildWeekSelectorBottomSheet(),
    );
  }

  /// 构建周次选择器底部弹窗
  Widget _buildWeekSelectorBottomSheet() {
    WeekModel tempSelectedWeek = _selectedWeek;

    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: 600.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            children: [
              // 顶部拖拽指示器和标题
              _buildBottomSheetHeader(),

              // 周次列表
              Expanded(
                child: _buildWeekList(tempSelectedWeek, (week) {
                  setModalState(() {
                    tempSelectedWeek = week;
                  });
                }),
              ),

              // 底部按钮
              _buildBottomSheetButtons(
                onCancel: () => Navigator.of(context).pop(),
                onConfirm: () {
                  _selectWeek(tempSelectedWeek);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建底部弹窗头部
  Widget _buildBottomSheetHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.h),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: AppTheme.black999.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 20.h),
          // 标题
          Text(
            '选择周次',
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.black333,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建周次列表
  Widget _buildWeekList(WeekModel selectedWeek, Function(WeekModel) onWeekTap) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      itemCount: _weeks.length,
      itemBuilder: (context, index) {
        final week = _weeks[index];
        final isSelected = week.weekNumber == selectedWeek.weekNumber;

        return _buildWeekItem(week, isSelected, () => onWeekTap(week));
      },
    );
  }

  /// 构建单个周次项
  Widget _buildWeekItem(WeekModel week, bool isSelected, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.blue2165f6.withValues(alpha: 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected ? AppTheme.blue2165f6 : AppTheme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    week.weekTitle,
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                      color: isSelected ? AppTheme.blue2165f6 : AppTheme.black333,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    week.dateRange,
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: AppTheme.black666,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppTheme.blue2165f6,
                size: 48.w,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildBottomSheetButtons({
    required VoidCallback onCancel,
    required VoidCallback onConfirm,
  }) {
    return Container(
      padding: EdgeInsets.all(40.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: onCancel,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.black666),
                padding: EdgeInsets.symmetric(vertical: 24.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: AppTheme.black666,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(width: 32.w),
          Expanded(
            child: ElevatedButton(
              onPressed: onConfirm,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.blue2165f6,
                padding: EdgeInsets.symmetric(vertical: 24.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                elevation: 0,
              ),
              child: Text(
                '确定',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 选择周次
  void _selectWeek(WeekModel week) {
    setState(() {
      _selectedWeek = week;
    });

    // 模拟刷新数据
    _refreshDataForSelectedWeek();

    // 显示选择成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换到${week.weekTitle}'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppTheme.blue2165f6,
      ),
    );
  }

  /// 为选中的周次刷新数据
  void _refreshDataForSelectedWeek() {
    // 这里可以根据选中的周次重新加载数据
    // 目前使用模拟数据，实际项目中应该调用API
    debugPrint('刷新第${_selectedWeek.weekNumber}周的数据');

    // 模拟数据加载延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          // 这里可以更新统计数据
          // 目前保持原有数据不变
        });
      }
    });
  }

  /// 构建统计数据区域
  Widget _buildStatisticsSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(40.w),
      child: Column(
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '周报提交',
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.black333,
                ),
              ),
              Text(
                '${_selectedWeek.weekTitle} ${_selectedWeek.dateRange}',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),
          // 统计数字行
          Row(
            children: [
              Expanded(
                child: _buildStatisticItem('6', '未提交'),
              ),
              Container(
                width: 1.w,
                height: 60.h,
                color: AppTheme.dividerColor,
              ),
              Expanded(
                child: _buildStatisticItem('38', '已提交'),
              ),
              Container(
                width: 1.w,
                height: 60.h,
                color: AppTheme.dividerColor,
              ),
              Expanded(
                child: _buildStatisticItem('24', '已批阅'),
              ),
              Container(
                width: 1.w,
                height: 60.h,
                color: AppTheme.dividerColor,
              ),
              Expanded(
                child: _buildStatisticItem('2', '待批阅'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatisticItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: TextStyle(
            fontSize: 48.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.blue2165f6,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black666,
          ),
        ),
      ],
    );
  }

  /// 构建学生列表
  Widget _buildStudentList(List<ReportStudentModel> students) {
    if (students.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 120.w,
              color: AppTheme.black999,
            ),
            SizedBox(height: 32.h),
            Text(
              '暂无数据',
              style: TextStyle(
                fontSize: 28.sp,
                color: AppTheme.black999,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
      itemCount: students.length,
      itemBuilder: (context, index) {
        final student = students[index];
        return StudentListItem(
          student: student,
          mode: StudentListItemMode.report,
          actionButtonText: !student.hasSubmitted ? '去提醒' : null,
          showActionButton: !student.hasSubmitted,
          onActionButtonTap: () => _showReminder(student),
        );
      },
    );
  }



  /// 显示提醒功能
  void _showReminder(ReportStudentModel student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提醒学生'),
        content: Text('确定要提醒 ${student.name} 提交周报吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendReminder(student);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 发送提醒
  void _sendReminder(ReportStudentModel student) {
    // TODO: 实现发送提醒功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已向 ${student.name} 发送提醒'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
