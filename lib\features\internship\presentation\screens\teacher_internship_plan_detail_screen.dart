/// -----
/// teacher_internship_plan_detail_screen.dart
///
/// 实习计划详情页面，展示实习计划的详细信息，包括基本信息、实习要求、考核评分等。
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';
import 'package:flutter_demo/core/widgets/empty_state_widget.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_plan.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_detail/plan_detail_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_detail/plan_detail_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_detail/plan_detail_state.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_application_screen.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_item.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_section_card.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:provider/provider.dart';

/// 人才培养方案展开/收起状态管理
class TalentDevelopmentPlanProvider with ChangeNotifier {
  bool _isExpanded = false;

  bool get isExpanded => _isExpanded;

  void toggleExpanded() {
    _isExpanded = !_isExpanded;
    notifyListeners();
  }
}

/// 实习计划详情页面
///
/// 展示实习计划的详细信息，包括基本信息、实习要求、考核评分、
/// 人才培养方案和安全教育考试等内容。
/// 
/// 支持学生端和教师端使用，学生端底部显示“免实习”和“实习申请”按钮。
///
/// 使用示例：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => TeacherInternshipPlanDetailScreen(
///       planId: '123',
///       isStudent: true, // 学生端显示底部按钮
///     ),
///   ),
/// );
/// ```
///
/// @see TeacherInternshipPlanListScreen 实习计划列表页面
class TeacherInternshipPlanDetailScreen extends StatelessWidget {
  /// 实习计划ID
  ///
  /// 用于从后端获取对应的实习计划详情数据
  /// 通过路由参数传入，例如：/teacher_internship_plan_detail/123
  final String planId;
  
  /// 是否为学生端
  /// 
  /// 如果为 true，则显示底部的“免实习”和“实习申请”按钮
  final bool isStudent;
  
  /// 免实习按钮点击回调
  final Function(String planId)? onExemptionPressed;
  
  /// 实习申请按钮点击回调
  final Function(String planId)? onApplyPressed;

  /// 创建实习计划详情页面
  ///
  /// @param key 小部件的键
  /// @param planId 实习计划ID，不能为空
  /// @param isStudent 是否为学生端，默认为 false
  /// @param onExemptionPressed 免实习按钮点击回调
  /// @param onApplyPressed 实习申请按钮点击回调
  const TeacherInternshipPlanDetailScreen({
    super.key,
    required this.planId,
    this.isStudent = false,
    this.onExemptionPressed,
    this.onApplyPressed,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<PlanDetailBloc>()
        ..add(LoadPlanDetailEvent(planId: planId)),
      child: ChangeNotifierProvider(
        create: (_) => TalentDevelopmentPlanProvider(),
        child: Scaffold(
          backgroundColor: AppTheme.backgroundColor,
          appBar: const CustomAppBar(
            title: '实习计划详情',
          ),
          body: BlocConsumer<PlanDetailBloc, PlanDetailState>(
            listener: (context, state) {
              if (state is PlanDetailRefreshErrorState) {
                AppSnackBar.showError(context, state.message);
              } else if (state is PlanDetailRefreshSuccessState) {
                AppSnackBar.showSuccess(context, '刷新成功');
              }
            },
            builder: (context, state) {
              if (state is PlanDetailLoadingState) {
                return const LoadingWidget();
              } else if (state is PlanDetailErrorState) {
                return EmptyStateWidget(
                  icon: Icons.error_outline,
                  title: '加载失败',
                  message: state.message,
                  buttonText: '重试',
                  onButtonPressed: () {
                    context.read<PlanDetailBloc>().add(
                      LoadPlanDetailEvent(planId: planId),
                    );
                  },
                );
              } else if (state is PlanDetailLoadedState ||
                         state is PlanDetailRefreshSuccessState ||
                         (state is PlanDetailRefreshErrorState && state.previousPlan != null)) {

                final plan = state is PlanDetailLoadedState
                    ? state.plan
                    : state is PlanDetailRefreshSuccessState
                        ? state.plan
                        : (state as PlanDetailRefreshErrorState).previousPlan!;

                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<PlanDetailBloc>().add(
                      RefreshPlanDetailEvent(planId: planId),
                    );
                  },
                  child: _buildDetailContent(plan),
                );
              }

              return const SizedBox.shrink();
            },
          ),
          // 如果是学生端，显示底部按钮
          bottomNavigationBar: isStudent ? _buildBottomButtons(context) : null,
        ),
      ),
    );
  }

  /// 构建详情内容
  ///
  /// @param plan 实习计划实体
  /// @return 构建好的详情内容Widget
  Widget _buildDetailContent(InternshipPlan plan) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: _buildPlanTitleSection(plan),
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: _buildPlanInfoListSection(plan),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 0.w, right: 0.w),
            child: InfoSectionCard(
                title: '实习要求',
                icon: Image.asset('assets/images/job_information_icon.png',
                    width: 25.w, height: 28.h),
                child: _buildInternshipRequirementsSection(plan)),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 0.w, right: 0.w),
            child: InfoSectionCard(
              title: '考核评分',
              icon: Image.asset('assets/images/star_score_icon.png',
                  width: 25.w, height: 28.h),
              child: _buildAssessmentScoresSection(plan),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 0.w, right: 0.w),
            child: InfoSectionCard(
              title: '人才培养方案',
              icon: Image.asset('assets/images/internship_information_icon.png',
                  width: 25.w, height: 28.h),
              child: _buildTalentDevelopmentPlanSection(),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 20.h, left: 0.w, right: 0.w),
            child: InfoSectionCard(
              title: '安全教育考试',
              icon: Image.asset('assets/images/internship_edu_exam_icon.png',
                  width: 25.w, height: 28.h),
              child: _buildSafetyEducationTestSection(),
            ),
          ),
          SizedBox(height: isStudent ? 100.h : 50.h), // 学生端增加底部空间
        ],
      ),
    );
  }

  /// 构建部分标题
  ///
  /// @param title 标题文本
  /// @param isExpandable 是否可展开，默认为false
  /// @param isExpanded 当前是否已展开，默认为false
  /// @param onTap 点击回调函数
  /// @return 构建好的标题Widget
  Widget _buildSectionTitle(
    String title, {
    bool isExpandable = false,
    bool isExpanded = false,
    VoidCallback? onTap,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333),
        ),
        if (isExpandable)
          TextButton(
            onPressed: onTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isExpanded ? '收起' : '展开',
                  style:
                      TextStyle(fontSize: 14.sp, color: AppTheme.primaryColor),
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: AppTheme.primaryColor,
                  size: 20.sp,
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建计划标题部分
  ///
  /// 显示实习计划的标题和状态信息
  ///
  /// @param plan 实习计划实体
  /// @return 构建好的标题部分Widget
  Widget _buildPlanTitleSection(InternshipPlan plan) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 24.h, vertical: 16.h),
      child: Text.rich(
        TextSpan(
          children: [
            TextSpan(
              text: plan.planName,
              style: TextStyle(
                fontSize: 30.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.black333,
              ),
            ),
            WidgetSpan(
              child: Container(
                margin: EdgeInsets.only(left: 20.w),
                padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: const Color(0xFFEBF1FF),
                  borderRadius: BorderRadius.circular(6.r),
                  border: Border.all(color: const Color(0x00ebf1ff), width: 0.5),
                ),
                child: Text(
                  _getPlanStatusText(plan.planStatus),
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建计划信息列表部分
  ///
  /// 显示实习计划的基本信息，如实习类型、周期等
  ///
  /// @param plan 实习计划实体
  /// @return 构建好的信息列表Widget
  Widget _buildPlanInfoListSection(InternshipPlan plan) {
    return Container(
      width: double.infinity,
      // padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InfoItem(label: '实习类型', value: _getPlanTypeText(plan.planType)),
          InfoItem(label: '实习周期', value: _formatDateRange(plan.startTime, plan.endTime)),
          InfoItem(label: '专业', value: plan.majorName),
          InfoItem(label: '层次', value: plan.level),
          InfoItem(label: '实习学期', value: plan.semester),
          InfoItem(label: '实习形式', value: _getPracticeModeText(plan.practiceMode)),
          InfoItem(label: '创建人', value: plan.createPerson),
        ],
      ),
    );
  }

  /// 构建实习要求部分
  ///
  /// 显示实习的各项要求，如应签到数、应交日报数等
  ///
  /// @param plan 实习计划实体
  /// @return 构建好的实习要求部分Widget
  Widget _buildInternshipRequirementsSection(InternshipPlan plan) {
    // 使用真实数据
    final List<Map<String, String>> requirements = [
      {'label': '应签到数', 'value': plan.signNum.toString()},
      {'label': '应交日报数', 'value': plan.dailyReportNum.toString()},
      {'label': '应交周报数', 'value': plan.weeklyReportNum.toString()},
      {'label': '应交月报数', 'value': plan.monthlyReportNum.toString()},
      {'label': '应交总结数', 'value': plan.summaryNum.toString()},
    ];
    
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 16.w),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 每行3个
        ),
        itemCount: requirements.length,
        itemBuilder: (context, index) {

          // 添加右侧和下侧的分割线
          return Container(
            child: _buildRequirementItem(
              requirements[index]['label']!,
              requirements[index]['value']!,
            ),
          );
        },
      ),
    );
  }

  /// 构建单个要求项
  ///
  /// @param label 标签文本
  /// @param value 值文本
  /// @return 构建好的要求项Widget
  Widget _buildRequirementItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 22.sp,
              color: const Color(0xFF999999),
            ),
          ),
          SizedBox(height: 18.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 48.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建考核评分部分
  ///
  /// 显示校内指导老师评分、企业指导老师评分等信息
  ///
  /// @param plan 实习计划实体
  /// @return 构建好的考核评分部分Widget
  Widget _buildAssessmentScoresSection(InternshipPlan plan) {
    final List<Map<String, String>> scoreItems = [
      {'label': '校内指导老师评分', 'score': '0'}, // 暂时使用默认值，等待后端数据
      {'label': '企业指导老师评分', 'score': '0'}, // 暂时使用默认值，等待后端数据
      {'label': '过程评分', 'score': '0'}, // 暂时使用默认值，等待后端数据
    ];

    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h, bottom: 0.h),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisExtent: 120.h,
          mainAxisSpacing: 1,
          crossAxisSpacing: 1,
        ),
        itemCount: scoreItems.length,
        itemBuilder: (context, index) {
          final item = scoreItems[index];
          return _buildScoreItem(item['label']!, item['score']!);
        },
      ),
    );
  }

  /// 构建分数项
  ///
  /// @param label 标签文本
  /// @param score 分数文本
  /// @return 构建好的分数项Widget
  Widget _buildScoreItem(String label, String score) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 22.sp,
            color: AppTheme.black999,
          ),
        ),
        SizedBox(height: 32.h),
        Text(
          score,
          style: TextStyle(
            fontSize: 48.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF02CC79),
          ),
        ),
      ],
    );
  }

  /// 构建人才培养方案部分
  ///
  /// 显示人才培养方案的详细内容，支持展开/收起
  ///
  /// @param isExpanded 是否已展开
  /// @return 构建好的人才培养方案部分Widget
  Widget _buildTalentDevelopmentPlanSection({bool isExpanded = false}) {
    const fullText =
        '这是人才培养方案的总结研究专业发展战略、人才培养定位、明确专业建设目标与建设思路、确定专业特色和优势建设方向。各专业成立专业建设委员会，委员会由校外专家、行业专业、校内专业专家构成，负责专业建设和人才培养方案制定工作。';

    return Padding(
      padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w,top: 16.0.h,bottom: 40.0.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8.h),
          Text(
            isExpanded ? fullText : '${fullText.substring(0, 60)}...',
            style: TextStyle(
                fontSize: 26.sp, color: AppTheme.black666, height: 1.5),
            maxLines: isExpanded ? null : 2,
            overflow:
            isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建安全教育考试部分
  ///
  /// 显示安全教育考试的相关信息
  ///
  /// @return 构建好的安全教育考试部分Widget
  Widget _buildSafetyEducationTestSection() {
    return Padding(
      padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w,top: 39.0.h,bottom: 49.0.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(Icons.assignment_outlined,
                  size: 18.sp, color: AppTheme.black666),
              SizedBox(width: 8.w),
              Text(
                '共计: 300道 10道判断题 20道选择题.',
                style: TextStyle(fontSize: 24.sp, color: AppTheme.black666),
              ),
            ],
          ),
          TextButton(
            onPressed: () {
              Logger.debug('实习计划详情页面', '点击查看安全教育考试明细');
            },
            style: TextButton.styleFrom(
              padding: EdgeInsets.zero,
              minimumSize: Size(0, 0),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('查看明细',
                    style: TextStyle(
                        fontSize: 24.sp, color: AppTheme.primaryColor)),
                Icon(Icons.chevron_right,
                    size: 16, color: AppTheme.primaryColor),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建底部按钮
  /// 
  /// 学生端显示“免实习”和“实习申请”按钮
  /// 
  /// @param context 构建上下文
  /// @return 构建好的底部按钮Widget
  Widget _buildBottomButtons(BuildContext context) {
    return Container(
      height: 100.h,
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          // 免实习按钮
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (onExemptionPressed != null) {
                  onExemptionPressed!(planId);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: AppTheme.primaryColor,
                side: BorderSide(color: AppTheme.primaryColor, width: 1),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                padding: EdgeInsets.symmetric(vertical: 16.h),
              ),
              child: Text(
                '免实习',
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(width: 20.w),
          // 实习申请按钮
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                // 无论是否提供了回调，都直接跳转到实习申请页面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => InternshipApplicationScreen(
                      planId: planId,
                    ),
                  ),
                );
                
                // 如果提供了回调，在跳转后调用回调
                if (onApplyPressed != null) {
                  onApplyPressed!(planId);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                padding: EdgeInsets.symmetric(vertical: 16.h),
              ),
              child: Text(
                '实习申请',
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取计划状态文本
  String _getPlanStatusText(int status) {
    switch (status) {
      case 0:
        return '待启用';
      case 1:
        return '进行中';
      case 2:
        return '已结束';
      default:
        return '未知';
    }
  }

  /// 获取计划类型文本
  String _getPlanTypeText(int type) {
    switch (type) {
      case 1:
        return '岗位实习';
      case 2:
        return '认识实习';
      case 3:
        return '其他实习';
      case 4:
        return '学徒制';
      case 5:
        return '综合实训';
      case 6:
        return '工学交替';
      default:
        return '未知类型';
    }
  }

  /// 获取实习形式文本
  String _getPracticeModeText(int mode) {
    switch (mode) {
      case 1:
        return '校外实习';
      case 2:
        return '校内真实职业场景';
      case 3:
        return '虚拟仿真';
      default:
        return '未知形式';
    }
  }

  /// 格式化日期范围
  String _formatDateRange(int startTime, int endTime) {
    if (startTime == 0 || endTime == 0) return '';

    final startDate = DateTime.fromMillisecondsSinceEpoch(startTime);
    final endDate = DateTime.fromMillisecondsSinceEpoch(endTime);

    final startStr = '${startDate.year}.${startDate.month.toString().padLeft(2, '0')}.${startDate.day.toString().padLeft(2, '0')}';
    final endStr = '${endDate.year}.${endDate.month.toString().padLeft(2, '0')}.${endDate.day.toString().padLeft(2, '0')}';

    return '$startStr-$endStr';
  }
}
