
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class SignLocationRowWidget extends StatelessWidget {
  const SignLocationRowWidget({required this.address, super.key});
  final String address;


  @override
  Widget build(BuildContext context) {
    return Row(children: [
      const Icon(
        Icons.location_on,
        size: 18,
        color: const Color(0xFF666666),
      ),
      SizedBox(width: 8.w),
      Expanded(
        child: Text(
          '当前位置：广东省深圳市南山区德白大厦1120号...',
          style: TextStyle(
            fontSize: 22.sp,
            color: AppTheme.black999,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
      Text(
        '调整位置',
        style: TextStyle(
          fontSize: 28.sp,
          color: const Color(0xFF2165F6),
        ),
      ),
      Sized<PERSON>ox(width: 8.w),
      Icon(
        Icons.chevron_right,
        size: 32.w,
        color: const Color(0xFF2165F6),
      ),
    ]);
  }

}