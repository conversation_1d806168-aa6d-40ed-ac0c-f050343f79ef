/// -----------------------------------------------------------------------------
/// data_screen.dart
///
/// 实习数据页面，用于展示实习生的各类实习数据和统计信息
///
/// 功能：
/// 1. 展示实习学年信息
/// 2. 展示实习信息（实习天数、免实习天数、安全教育成绩、考核成绩）
/// 3. 展示实习报告信息（日报、周报、月报、总结）
/// 4. 展示签到信息（签到、签到偏移、免签天数、请假天数）
///
/// 使用方法：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(builder: (context) => const DataScreen()),
/// );
/// ```
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'internship_statistics_screen.dart';
import 'report_statistics_screen.dart';
import 'checkin_statistics_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 虚线绘制器
///
/// 用于绘制水平虚线
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    required this.dashWidth,
    required this.dashSpace,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double startX = 0;
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.height;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(DashedLinePainter oldDelegate) =>
      oldDelegate.color != color ||
      oldDelegate.dashWidth != dashWidth ||
      oldDelegate.dashSpace != dashSpace;
}

/// 实习数据页面
///
/// 展示实习生的各类实习数据和统计信息，包括实习信息、实习报告和签到信息等
class DataScreen extends StatefulWidget {
  /// 创建实习数据页面
  ///
  /// [key] 组件的唯一标识符
  const DataScreen({Key? key}) : super(key: key);

  @override
  State<DataScreen> createState() => _DataScreenState();
}

/// 实习数据页面状态
class _DataScreenState extends State<DataScreen> {
  /// 模拟数据
  Map<String, dynamic>? _data;

  /// 是否正在加载
  bool _isLoading = true;

  /// 是否已经加载过数据
  bool _hasLoadedData = false;

  @override
  void initState() {
    super.initState();
    // 首次进入页面时加载数据
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    if (_hasLoadedData) return; // 如果已经加载过数据，不再重复加载

    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟网络请求，延迟3秒
      await Future.delayed(const Duration(seconds: 1));

      // 模拟获取数据
      final data = {
        'internshipYear': '2022级市场销售2024-2025年实习学年第二期岗位实习',
        'status': ['实习中', '已绑定岗位'],
        'statistics': {
          'binding': '69/259',
          'pending': '5/600',
          'ongoing': '60/600',
          'exemption': '20/100',
          'agreement': '69/259',
          'insurance': '103/156',
          'safetyExam': '103/156',
          'examPass': '5/103',
        },
        'reports': {
          'daily': '20/100',
          'monthly': '1/6',
          'weekly': '20/100',
          'summary': '6/8',
          'dailyReviewRate': '20/100',
          'monthlyReviewRate': '1/6',
          'weeklyReviewRate': '20/100',
          'summaryReviewRate': '6/8',
          'dailyTimelyRate': '20/100',
          'monthlyTimelyRate': '1/6',
          'weeklyTimelyRate': '20/100',
          'summaryTimelyRate': '6/8',
        },
        'attendance': {
          'checkin': '30/100',
          'offset': '5/30',
        }
      };

      if (mounted) {
        setState(() {
          _data = data;
          _isLoading = false;
          _hasLoadedData = true; // 标记已加载过数据
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载失败: $e')),
        );
      }
    }
  }

  /// 构建实习数据页面的用户界面
  ///
  /// 创建包含实习学年信息、实习信息、实习报告和签到信息的完整页面
  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      // 移除AppBar，使用自定义顶部区域
      body: _isLoading
        ? const Center(child: CircularProgressIndicator())
        : Stack(
            children: [
              // 顶部背景图片，延伸到状态栏
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  width: double.infinity,
                  height: 475.h,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/data_top_bg.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                  // 设置状态栏样式
                  child: AnnotatedRegion<SystemUiOverlayStyle>(
                    value: const SystemUiOverlayStyle(
                      statusBarColor: Colors.transparent,
                      statusBarIconBrightness: Brightness.light,
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 30.h),
                            // 标题和下拉箭头
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Text(
                                    '2021级市场销售2023-2024实习学年第二学期岗位实习',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 32.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.keyboard_arrow_down,
                                  color: Colors.white,
                                  size: 40.sp,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 内容区域
              Padding(
                padding: EdgeInsets.only(top: statusBarHeight + 100.h),
                child: _buildContent(),
              ),


            ],
          ),
    );
  }

  /// 构建页面内容
  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 44.h),
          // 实习信息板块
          _buildInternshipInfoSection(),

          // 实习报告板块
          _buildInternshipReportSection(),

          // 签到信息板块
          _buildSignInfoSection(),

          // 底部填充，以防被导航栏遮挡
          const SizedBox(height: 70),
        ],
      ),
    );
  }



  /// 构建实习信息区块
  ///
  /// 根据设计图创建实习信息区块，包含绑定、待实习、实习中、免实习四个数据项
  Widget _buildInternshipInfoSection() {
    return Container(
      width: double.infinity,
      height: 244.h,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F9FF),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Material(
            color: const Color(0xffedf2ff),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child:
            // 标题部分
            Row(
              children: [
                // 图标
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 30.h),
                  child: Image.asset(
                    'assets/images/data_internship_info_icon.png',
                    width: 34.w,
                    height: 34.h,
                  ),
                ),
                SizedBox(width: 22.w),
                // 标题
                Text(
                  '实习信息',
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.black333,
                  ),
                ),
              ],
            ),
          ),
          // 数据项部分
          Expanded(
            child: Row(
              children: [
                _buildInternshipInfoItem('绑定', '69', '259'),
                _buildVerticalDivider(),
                _buildInternshipInfoItem('待实习', '46', '126'),
                _buildVerticalDivider(),
                _buildInternshipInfoItem('实习中', '38', '287'),
                _buildVerticalDivider(),
                _buildInternshipInfoItem('免实习', '2', '62'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建实习信息数据项
  ///
  /// 创建一个包含标题、数值和分母的数据项
  Widget _buildInternshipInfoItem(String title, String value, String total) {
    // 判断是否可点击
    bool isClickable = title == '绑定' || title == '待实习' || title == '实习中' || title == '免实习';

    // 确定点击类型
    String clickType = '';
    if (title == '绑定') {
      clickType = 'binding';
    } else if (title == '待实习') {
      clickType = 'pending';
    } else if (title == '实习中') {
      clickType = 'ongoing';
    }

    return Expanded(
      child: InkWell(
        onTap: isClickable ? () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const InternshipStatisticsScreen(),
            ),
          );
        } : null,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 38.h),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 22.sp,
                  color: AppTheme.black999,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10.h),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.black333,
                      ),
                    ),
                    TextSpan(
                      text: '/$total',
                      style: TextStyle(
                        fontSize: 22.sp,
                        color: AppTheme.black999,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建垂直分隔线
  Widget _buildVerticalDivider() {
    return Container(
      height: 78.h,
      width: 1.w,
      color: Colors.grey.withOpacity(0.2),
    );
  }

  /// 构建实习报告区块
  ///
  /// 根据设计图创建实习报告区块，包含日报、月报、周报、总结等数据项
  Widget _buildInternshipReportSection() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题部分
          Material(
            color: const Color(0xFFFFF9E6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                // 图标
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 30.h),
                  child: Image.asset(
                    'assets/images/data_internship_report_icon.png',
                    width: 34.w,
                    height: 34.h,
                  ),
                ),
                SizedBox(width: 22.w),
                // 标题
                Text(
                  '实习报告',
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.black333,
                  ),
                ),
              ],
            ),
          ),

          // 第一行：报告提交情况
          _buildReportRow([
            _buildReportItem('日报', '69', '259'),
            _buildReportItem('月报', '46', '126'),
            _buildReportItem('周报', '38', '287'),
            _buildReportItem('总结', '2', '62'),
          ]),

          // 分隔线
          Divider(height: 1.h, thickness: 1.h, color: Colors.grey.withOpacity(0.1)),

          // 第二行：报告批阅率
          _buildReportRow([
            _buildReportItem('日报批阅率', '69', '259'),
            _buildReportItem('月报批阅率', '46', '126'),
            _buildReportItem('周报批阅率', '38', '287'),
            _buildReportItem('总结批阅率', '2', '62'),
          ]),

          // 分隔线
          Divider(height: 1.h, thickness: 1.h, color: Colors.grey.withOpacity(0.1)),

          // 第三行：报告及时批阅率
          _buildReportRow([
            _buildReportItem('日报及时批阅率', '69', '259'),
            _buildReportItem('月报及时批阅率', '46', '126'),
            _buildReportItem('周报及时批阅率', '69', '259'),
            _buildReportItem('总结及时批阅率', '46', '126'),
          ]),
        ],
      ),
    );
  }

  /// 构建报告行
  ///
  /// 创建一行包含多个报告项的容器
  Widget _buildReportRow(List<Widget> items) {
    return Row(
      children: items,
    );
  }

  /// 构建报告项
  ///
  /// 创建一个包含标题、数值和分母的报告数据项
  Widget _buildReportItem(String title, String value, String total) {
    // 判断是否可点击（周报可点击跳转到报告统计页面）
    bool isClickable = title == '周报' || '日报' == title || '月报' == title ||  '总结' == title;

    return Expanded(
      child: InkWell(
        onTap: isClickable ? () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ReportStatisticsScreen(),
            ),
          );
        } : null,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(
                color: Colors.grey.withOpacity(0.1),
                width: 1.w,
              ),
            ),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 22.sp,
                  color: AppTheme.black999,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10.h),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.black333,
                      ),
                    ),
                    TextSpan(
                      text: '/$total',
                      style: TextStyle(
                        fontSize: 22.sp,
                        color: AppTheme.black999,
                      ),
                    ),
                  ],
                ),
              ),
              if (isClickable) ...[
                SizedBox(height: 6.h),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.sp,
                  color: Colors.grey[400],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建签到信息区块
  ///
  /// 根据设计图创建签到信息区块，包含签到和签到偏移数据项
  Widget _buildSignInfoSection() {
    return Container(
      width: double.infinity,
      height: 244.h,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF5FFFD),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题部分
          Material(
            color: const Color(0xFFE6FFFC),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                // 图标
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 30.h),
                  child: Image.asset(
                    'assets/images/data_sign_info_icon.png',
                    width: 34.w,
                    height: 34.h,
                  ),
                ),
                SizedBox(width: 22.w),
                // 标题
                Text(
                  '签到信息',
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.black333,
                  ),
                ),
              ],
            ),
          ),

          // 数据项部分
          Expanded(
            child: Row(
              children: [
                _buildSignInfoItem('签到', '69', '259'),
                _buildVerticalDivider(),
                _buildSignInfoItem('签到偏移', '46', '126'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建签到信息数据项
  ///
  /// 创建一个包含标题、数值和分母的签到数据项
  Widget _buildSignInfoItem(String title, String value, String total) {
    // 判断是否可点击（签到可点击跳转到签到统计页面）
    bool isClickable = title == '签到';

    return Expanded(
      child: InkWell(
        onTap: isClickable ? () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CheckinStatisticsScreen(),
            ),
          );
        } : null,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 38.h),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 22.sp,
                  color: AppTheme.black999,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10.h),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: value,
                      style: TextStyle(
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.black333,
                      ),
                    ),
                    TextSpan(
                      text: '/$total',
                      style: TextStyle(
                        fontSize: 22.sp,
                        color: AppTheme.black999,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  /// 构建一个包含标题和内容的数据区块
  ///
  /// 创建一个带有标题和内容的数据展示区块，用于展示不同类别的实习数据
  ///
  /// 参数:
  /// - [title] 区块标题，如“实习信息”、“实习报告”等
  /// - [children] 区块内容组件列表，通常是多个信息行
  Widget _buildSection({
    required String title,
    required List<Widget> children,
    IconData? icon,
    Color? iconColor,
  }) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      padding: EdgeInsets.symmetric(vertical: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 区块标题
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
            child: Row(
              children: [
                // 图标
                if (icon != null)
                  Container(
                    width: 40.w,
                    height: 40.h,
                    margin: EdgeInsets.only(right: 10.w),
                    decoration: BoxDecoration(
                      color: (iconColor ?? Colors.blue).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: iconColor ?? Colors.blue,
                      size: 24.sp,
                    ),
                  ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 15.h),
          // 区块内容
          ...children,
        ],
      ),
    );
  }

  /// 构建一行信息项
  ///
  /// 创建一行包含多个信息项的容器，通常用于并排显示多个相关的数据项
  ///
  /// 参数:
  /// - [items] 要显示的信息项组件列表，通常是由 _buildInfoItem 创建的组件
  Widget _buildInfoRow(List<Widget> items) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: items,
      ),
    );
  }

  /// 构建单个信息项
  ///
  /// 创建一个包含标题和数值的信息项，用于展示单个数据指标
  ///
  /// 参数:
  /// - [title] 信息项标题，如“实习天数”、“日报”等
  /// - [value] 信息项的数值，如“69/259”、“95”等
  /// - [valueColor] 数值的显示颜色
  Widget _buildInfoItem(String title, String value, Color valueColor) {
    // 判断是否可点击
    bool isClickable = title == '绑定' || title == '待实习' || title == '实习中' || title == '免实习';

    return Expanded(
      child: InkWell(
        onTap: isClickable ? () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const InternshipStatisticsScreen(),
            ),
          );
        } : null,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 4.w),
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(color: Colors.grey.withOpacity(0.1), width: 1),
            ),
          ),
          child: Column(
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 26.sp,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 12.h),
              Text(
                value,
                style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.w600,
                  color: valueColor,
                  letterSpacing: -0.5,
                ),
                textAlign: TextAlign.center,
              ),
              if (isClickable) ...[  // 可点击项添加小图标
                SizedBox(height: 6.h),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 20.sp,
                  color: Colors.grey[400],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}