import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/config/injection/injection.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/storage/local_storage.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/debug/presentation/screens/api_debug_screen.dart';
import 'package:flutter_demo/features/profile/presentation/screens/change_password_screen.dart';
import 'package:flutter_demo/features/settings/presentation/screens/about_us_screen.dart';
import 'package:flutter_demo/features/settings/presentation/screens/version_info_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:go_router/go_router.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String cacheSize = '10.00 MB';
  late LocalStorage _localStorage;
  bool _isLoggingOut = false;

  @override
  void initState() {
    super.initState();
    _localStorage = getIt<LocalStorage>();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: '系统设置',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          _buildSettingsList(),
          const Spacer(),
          _buildLogoutButton(),
          const SizedBox(height: 30), // 底部留白
        ],
      ),
    );
  }

  Widget _buildSettingsList() {
    return Column(
      children: [
        _buildSettingItem(
          title: '清除缓存',
          trailing: Text(
            cacheSize,
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 16,
            ),
          ),
          onTap: _clearCache,
        ),
        _buildDivider(),
        _buildSettingItem(
          title: '修改密码',
          onTap: _navigateToChangePassword,
        ),
        _buildDivider(),
        _buildSettingItem(
          title: '关于我们',
          onTap: _navigateToAbout,
        ),
        _buildDivider(),
        _buildSettingItem(
          title: '版本说明',
          onTap: _showVersionInfo,
        ),
        _buildDivider(),
        _buildSettingItem(
          title: '好评鼓励',
          onTap: _rateApp,
        ),
        // 仅在调试模式下显示API调试入口
        if (kDebugMode) ...[
          _buildDivider(),
          _buildSettingItem(
            title: 'API调试工具',
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red[400],
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Text(
                '开发者',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ),
            onTap: _navigateToApiDebug,
          ),
        ],
      ],
    );
  }

  Widget _buildSettingItem({
    required String title,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 30.sp,
                fontWeight: FontWeight.w500,
                color: AppTheme.black333,
              ),
            ),
            const Spacer(),
            if (trailing != null) trailing,
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      child: Divider(
        height: 0.5,
        thickness: 1,
        color: Colors.grey[200],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _logout,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 14),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: const Text(
            '退出登录',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  // 清除缓存
  Future<void> _clearCache() async {
    // 显示确认对话框
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清除缓存？'),
        content: const Text('这将清除应用的临时数据，不会影响您的账户信息。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确认'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    // 显示加载指示器
    if (!mounted) return;
    final BuildContext dialogContext = context;
    await showDialog(
      context: dialogContext,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // 模拟清除缓存的过程
    await Future.delayed(const Duration(seconds: 1));

    // 清除缓存（示例，实际应用中可能需要更复杂的逻辑）
    try {
      // 这里应该使用适当的缓存清理逻辑，而不是清除所有SharedPreferences
      // 因为这会清除用户登录信息
      // 实际应用中应该只清除缓存相关的数据
      setState(() {
        cacheSize = '0.00 MB';
      });
    } on Exception catch (e) {
      // 处理错误情况
      debugPrint('清除缓存失败: $e');
    }

    // 关闭加载指示器并显示成功消息
    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('缓存已清除')),
      );
    }
  }

  // 意见反馈页面导航
  void _navigateToChangePassword() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ChangePasswordScreen(),
      ),
    );
  }

  // 关于我们页面导航
  void _navigateToAbout() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AboutUsScreen(),
      ),
    );
  }

  // 显示版本信息
  void _showVersionInfo() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const VersionInfoScreen(),
      ),
    );
  }

  // 应用评分
  void _rateApp() {
    // 实现应用评分的逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('感谢您的支持！')),
    );
  }

  // 导航到API调试工具
  void _navigateToApiDebug() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ApiDebugScreen(),
      ),
    );
  }

  // 退出登录
  Future<void> _logout() async {
    // 显示确认对话框
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出登录？'),
        content: const Text('您确定要退出当前账号吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确认'),
          ),
        ],
      ),
    );

    if (confirm != true) {
      return;
    }

    // 设置退出登录状态
    setState(() {
      _isLoggingOut = true;
    });

    try {
      // 直接清空本地缓存，不调用API
      await _clearAuthCache();

      // 显示成功消息
      if (mounted) {
        AppSnackBar.showSuccess(context, '已成功退出登录');

        // 延迟一下再跳转，让用户看到提示
        await Future.delayed(const Duration(milliseconds: 1500));

        // 跳转到登录页面
        if (mounted) {
          _navigateToLogin();
        }
      }
    } on Exception {
      // 即使清理缓存失败，也要跳转到登录页面
      if (mounted) {
        AppSnackBar.showError(context, '退出登录时发生错误，但已清除本地数据');
        _navigateToLogin();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoggingOut = false;
        });
      }
    }
  }

  /// 清空认证相关的本地缓存
  Future<void> _clearAuthCache() async {
    // 清除所有与用户认证相关的数据
    await Future.wait([
      _localStorage.remove(AppConstants.tokenKey),
      _localStorage.remove(AppConstants.userKey),
      _localStorage.remove(AppConstants.userNameKey),
      _localStorage.remove(AppConstants.userPhoneKey),
      _localStorage.remove(AppConstants.userTypeKey),
      _localStorage.remove(AppConstants.isLoggedInKey),
      // 可选：清除记住的登录信息
      _localStorage.remove(AppConstants.phoneKey),
      _localStorage.remove(AppConstants.passwordKey),
    ]);
  }

  /// 导航到登录页面
  void _navigateToLogin() {
    // 使用go_router导航到登录页面，清除所有路由历史
    if (mounted) {
      context.go('/login');
    }
  }
}