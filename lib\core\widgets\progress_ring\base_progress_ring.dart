/// -----
/// base_progress_ring.dart
/// 
/// 基础圆环进度组件，只负责绘制圆环进度，不包含文字内容
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 基础圆环进度组件
/// 
/// 只负责绘制圆环进度，不包含任何文字内容
/// 可以独立使用或与其他组件组合使用
class BaseProgressRing extends StatelessWidget {
  /// 进度值 (0.0 - 1.0)
  final double progress;
  
  /// 圆环大小
  final double size;
  
  /// 圆环线宽
  final double strokeWidth;
  
  /// 进度圆环颜色
  final Color progressColor;
  
  /// 背景圆环颜色
  final Color backgroundColor;
  
  /// 圆环起始角度（弧度）
  final double startAngle;
  
  /// 是否使用圆形端点
  final bool useRoundCap;
  
  /// 是否启用动画
  final bool enableAnimation;
  
  /// 动画持续时间
  final Duration animationDuration;

  const BaseProgressRing({
    Key? key,
    required this.progress,
    this.size = 160,
    this.strokeWidth = 10,
    this.progressColor = const Color(0xFF2979FF),
    this.backgroundColor = const Color(0xFFEEEEEE),
    this.startAngle = -pi / 2, // 从顶部开始
    this.useRoundCap = true,
    this.enableAnimation = false,
    this.animationDuration = const Duration(milliseconds: 800),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (enableAnimation) {
      return _AnimatedProgressRing(
        progress: progress,
        size: size,
        strokeWidth: strokeWidth,
        progressColor: progressColor,
        backgroundColor: backgroundColor,
        startAngle: startAngle,
        useRoundCap: useRoundCap,
        animationDuration: animationDuration,
      );
    }

    return SizedBox(
      width: size.w,
      height: size.w,
      child: CustomPaint(
        painter: ProgressRingPainter(
          progress: progress,
          progressColor: progressColor,
          backgroundColor: backgroundColor,
          strokeWidth: strokeWidth.w,
          startAngle: startAngle,
          useRoundCap: useRoundCap,
        ),
      ),
    );
  }
}

/// 带动画的圆环进度组件
class _AnimatedProgressRing extends StatefulWidget {
  final double progress;
  final double size;
  final double strokeWidth;
  final Color progressColor;
  final Color backgroundColor;
  final double startAngle;
  final bool useRoundCap;
  final Duration animationDuration;

  const _AnimatedProgressRing({
    Key? key,
    required this.progress,
    required this.size,
    required this.strokeWidth,
    required this.progressColor,
    required this.backgroundColor,
    required this.startAngle,
    required this.useRoundCap,
    required this.animationDuration,
  }) : super(key: key);

  @override
  _AnimatedProgressRingState createState() => _AnimatedProgressRingState();
}

class _AnimatedProgressRingState extends State<_AnimatedProgressRing>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void didUpdateWidget(_AnimatedProgressRing oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.progress != widget.progress) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      _animationController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return SizedBox(
          width: widget.size.w,
          height: widget.size.w,
          child: CustomPaint(
            painter: ProgressRingPainter(
              progress: _animation.value,
              progressColor: widget.progressColor,
              backgroundColor: widget.backgroundColor,
              strokeWidth: widget.strokeWidth.w,
              startAngle: widget.startAngle,
              useRoundCap: widget.useRoundCap,
            ),
          ),
        );
      },
    );
  }
}

/// 圆环进度绘制器
class ProgressRingPainter extends CustomPainter {
  /// 进度值 (0.0 - 1.0)
  final double progress;
  
  /// 进度圆环颜色
  final Color progressColor;
  
  /// 背景圆环颜色
  final Color backgroundColor;
  
  /// 圆环线宽
  final double strokeWidth;
  
  /// 圆环起始角度（弧度）
  final double startAngle;
  
  /// 是否使用圆形端点
  final bool useRoundCap;

  ProgressRingPainter({
    required this.progress,
    required this.progressColor,
    required this.backgroundColor,
    required this.strokeWidth,
    required this.startAngle,
    required this.useRoundCap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    // 绘制背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = useRoundCap ? StrokeCap.round : StrokeCap.butt;
    
    canvas.drawCircle(center, radius, backgroundPaint);
    
    // 绘制进度圆环
    if (progress > 0) {
      final progressPaint = Paint()
        ..color = progressColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth
        ..strokeCap = useRoundCap ? StrokeCap.round : StrokeCap.butt;
      
      final rect = Rect.fromCircle(center: center, radius: radius);
      final sweepAngle = 2 * pi * progress;
      
      canvas.drawArc(rect, startAngle, sweepAngle, false, progressPaint);
    }
  }

  @override
  bool shouldRepaint(ProgressRingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.progressColor != progressColor ||
           oldDelegate.backgroundColor != backgroundColor ||
           oldDelegate.strokeWidth != strokeWidth ||
           oldDelegate.startAngle != startAngle ||
           oldDelegate.useRoundCap != useRoundCap;
  }
}
