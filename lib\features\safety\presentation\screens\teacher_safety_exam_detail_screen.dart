import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/safety/di/safety_injection.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_bloc.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_event.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_state.dart';
import 'package:flutter_demo/features/safety/presentation/widgets/exam_question_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';


/// 安全教育考试详情页面
///
/// 展示学生的安全教育考试情况，包括得分和每道题的答题情况
/// 集成了BLoC提供者功能，从API获取数据
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class SafetyExamDetailScreen extends StatelessWidget {
  /// 学生ID
  final String studentId;

  /// 学生姓名
  final String studentName;

  /// 考试记录ID
  final String recordId;

  const SafetyExamDetailScreen({
    Key? key,
    required this.studentId,
    required this.studentName,
    required this.recordId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<ExamDetailBloc>()..add(LoadExamDetailEvent(recordId: recordId)),
      child: _SafetyExamDetailView(
        studentId: studentId,
        studentName: studentName,
        recordId: recordId,
      ),
    );
  }
}

/// 安全教育考试详情视图
///
/// 实际的考试详情界面实现
class _SafetyExamDetailView extends StatelessWidget {
  /// 学生ID
  final String studentId;

  /// 学生姓名
  final String studentName;

  /// 考试记录ID
  final String recordId;

  const _SafetyExamDetailView({
    required this.studentId,
    required this.studentName,
    required this.recordId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF333333), size: 18),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '安全教育考试情况',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: BlocBuilder<ExamDetailBloc, ExamDetailState>(
        builder: (context, state) {
          if (state is ExamDetailLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is ExamDetailError) {
            return _buildErrorView(context, state.message);
          } else if (state is ExamDetailRefreshing) {
            return _buildExamContent(context, state.previousData, isRefreshing: true);
          } else if (state is ExamDetailLoaded) {
            return _buildExamContent(context, state.examDetail);
          }

          return const Center(child: Text('暂无数据'));
        },
      ),
    );
  }

  /// 构建错误视图
  Widget _buildErrorView(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(message),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<ExamDetailBloc>().add(LoadExamDetailEvent(recordId: recordId));
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建考试内容
  Widget _buildExamContent(BuildContext context, ExamDetailResponse examDetail, {bool isRefreshing = false}) {
    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: ListView(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          children: [
            // 考试得分
            Container(
              padding: EdgeInsets.symmetric(vertical: 44.h),
              child: Row(
                children: [
                  Text(
                    '考试得分',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${examDetail.score}分)',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF4B7CFF),
                    ),
                  ),
                ],
              ),
            ),

            // 试题列表
            ...examDetail.examQuestionList.asMap().entries.map<Widget>((entry) {
              final index = entry.key;
              final question = entry.value;
              return ExamQuestionItem(
                question: question,
                questionNumber: index + 1,
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

}
