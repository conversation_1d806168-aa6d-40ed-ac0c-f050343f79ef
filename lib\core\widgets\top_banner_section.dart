/// -----------------------------------------------------------------------------
/// top_banner_section.dart
///
/// 顶部横幅区域组件，用于显示带有背景图片、文字和通知栏的顶部区域。
/// 可用于首页、报告页面等需要顶部横幅的场景。
///
/// 特性：
/// 1. 支持背景图片延伸到状态栏
/// 2. 支持自定义欢迎文本和副标题
/// 3. 支持自定义通知栏内容和点击事件
/// 4. 支持图片加载失败时的备用显示
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 顶部横幅区域组件
///
/// 用于显示带有背景图片、文字和通知栏的顶部区域
class TopBannerSection extends StatelessWidget {
  /// 背景图片路径
  final String backgroundImagePath;

  /// 右侧图标路径
  final String rightIconPath;

  /// 欢迎标题
  final String welcomeTitle;

  /// 欢迎副标题
  final String welcomeSubtitle;

  /// 欢迎描述文本
  final String welcomeDescription;

  /// 通知文本
  final String noticeText;

  /// 通知栏点击回调
  final VoidCallback? onNoticeTap;

  /// 是否显示通知栏
  final bool showNoticeBar;

  /// 背景图片加载失败时的渐变颜色
  final List<Color> fallbackGradientColors;

  /// 右侧图标加载失败时的备用图标
  final IconData fallbackIconData;

  /// 构造函数
  const TopBannerSection({
    Key? key,
    required this.backgroundImagePath,
    required this.rightIconPath,
    required this.welcomeTitle,
    required this.welcomeSubtitle,
    required this.welcomeDescription,
    this.noticeText = '',
    this.onNoticeTap,
    this.showNoticeBar = true,
    this.fallbackGradientColors = const [Color(0xFF4169E1), Color(0xFF7B68EE)],
    this.fallbackIconData = Icons.smart_toy,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;
    print('statusBarHeight: $statusBarHeight');
    // 顶部图片总高度 = 状态栏高度 + 180
    final topBannerHeight = statusBarHeight + 230;
    print('topBannerHeight: $topBannerHeight');

    return Stack(
      children: [
        // 顶部区域 - 使用图片，延伸到状态栏
        Container(
          width: double.infinity,
          height: 280.h,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Image.asset(
            backgroundImagePath,
            width: double.infinity,
            height: 280.h,
            fit: BoxFit.fill,
            errorBuilder: (context, error, stackTrace) {
              // 如果图片加载失败，显示备用内容
              return Container(
                width: double.infinity,
                height: topBannerHeight,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: fallbackGradientColors,
                  ),
                ),
                child: Stack(
                  children: [
                    // 右侧图标
                    Positioned(
                      right: 20,
                      bottom: 20,
                      child: Image.asset(
                        rightIconPath,
                        width: 120,
                        height: 120,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            fallbackIconData,
                            color: Colors.white,
                            size: 80,
                          );
                        },
                      ),
                    ),
                    // 左侧文字 - 位置调整，考虑状态栏高度
                    Positioned(
                      left: 20,
                      top: statusBarHeight + 30, // 考虑状态栏高度
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            welcomeTitle,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            welcomeSubtitle,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            welcomeDescription,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),

        // 消息通知栏 - 仅在showNoticeBar为true时显示
        if (showNoticeBar)
          Positioned(
            left: 16,
            right: 16,
            top: topBannerHeight - 130, // 让通知栏更多地重叠在顶部图片上
            child: GestureDetector(
              onTap: onNoticeTap,
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const SizedBox(width: 16),
                    // 消息通知图标和文字
                    Image.asset(
                      'assets/images/home_notification_icon.png',
                      width: 142.w,
                      height: 26.h,
                    ),
                    const SizedBox(width: 16),
                    // 公告内容
                    Expanded(
                      child: Text(
                        noticeText,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 箭头
                    Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(width: 16),
                  ],
                ),
              ),
            ),
          ),

        // 添加一个空白区域，确保整个组件的高度正确
        SizedBox(
          height: showNoticeBar
              ? topBannerHeight-75
              : topBannerHeight-95,
        ),
      ],
    );
  }
}
