/// -----
/// safety_exam_notice_screen.dart
/// 
/// 安全教育考试须知页面
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_demo/core/router/route_constants.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';

/// 安全教育考试须知页面
class SafetyExamNoticeScreen extends StatelessWidget {
  const SafetyExamNoticeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '考试须知',
      ),
      body: Column(
        children: [
          // 课程信息区域
          _buildCourseInfoSection(),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(40.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 温馨提示区域
                  _buildNoticeSection(),
                ],
              ),
            ),
          ),

          // 底部开始考试按钮
          _buildStartExamButton(context),
        ],
      ),
    );
  }

  /// 构建课程信息区域
  Widget _buildCourseInfoSection() {
    return const Column(
      children: [
        // 使用CourseHeaderSection组件，自动从全局状态获取实习计划数据
        CourseHeaderSection(),

        // 状态标签区域

      ],
    );
  }

  /// 构建温馨提示区域
  Widget _buildNoticeSection() {
    return Container(
      padding: EdgeInsets.all(35.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '温馨提示',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          
          SizedBox(height: 40.h),
          
          _buildNoticeItem(
            '1.本次考试共20题，每题5分，考试考试后正式进入答题。',
          ),
          
          SizedBox(height: 30.h),
          
          _buildNoticeItem(
            '2.答对1题得5分，自动跳转下一题，答错不扣分并显示正确答案',
          ),
          
          SizedBox(height: 30.h),
          
          _buildNoticeItem(
            '3.答题结束后显示本次考试分数，学生可以再次测试。',
          ),
        ],
      ),
    );
  }

  /// 构建提示项
  Widget _buildNoticeItem(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 30.sp,
        color: AppTheme.black666,
        height: 1.5,
      ),
    );
  }

  /// 构建开始考试按钮
  Widget _buildStartExamButton(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: SizedBox(
        width: double.infinity,
        height: 100.h,
        child: ElevatedButton(
          onPressed: () {
            // 跳转到考试页面，替换当前页面（不保留在导航栈中）
            context.pushReplacement(AppRoutes.safetyExam);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2165F6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
            elevation: 0,
          ),
          child: Text(
            '开始考试',
            style: TextStyle(
              fontSize: 36.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
