/// -----
/// insurance_warning_screen.dart
/// 
/// 实习保险预警页面，用于显示学生保险覆盖情况和预警信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 保险状态枚举
enum InsuranceStatus {
  /// 未上传
  notUploaded,
  /// 即将到期
  expiringSoon,
  /// 已过期
  expired,
}

/// 保险预警记录模型
class InsuranceWarningRecord {
  final String studentName;
  final String phoneNumber;
  final int uncoveredDays;
  final InsuranceStatus status;
  final String avatarUrl;

  const InsuranceWarningRecord({
    required this.studentName,
    required this.phoneNumber,
    required this.uncoveredDays,
    required this.status,
    this.avatarUrl = '',
  });
}

class InsuranceWarningScreen extends StatelessWidget {
  const InsuranceWarningScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 模拟数据
    final List<InsuranceWarningRecord> warningRecords = [
      const InsuranceWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        uncoveredDays: 35,
        status: InsuranceStatus.notUploaded,
        avatarUrl: AppConstants.avatar1,
      ),
      const InsuranceWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        uncoveredDays: 5,
        status: InsuranceStatus.expiringSoon,
        avatarUrl: AppConstants.avatar2,
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '实习保险预警',
        centerTitle: true,
        showBackButton: true,
      ),
      body: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: warningRecords.length,
        itemBuilder: (context, index) {
          return _buildInsuranceWarningItem(warningRecords[index]);
        },
      ),
    );
  }

  /// 构建保险预警列表项
  Widget _buildInsuranceWarningItem(InsuranceWarningRecord record) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧头像
          _buildAvatar(record.studentName, avatarUrl: record.avatarUrl),
          
          SizedBox(width: 24.w),
          
          // 中间信息
          Expanded(
            child: _buildStudentInfo(record),
          ),
          
          SizedBox(width: 20.w),
          
          // 右侧状态标签
          _buildStatusTag(record.status),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(String studentName, {String? avatarUrl}) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: avatarUrl != null && avatarUrl.isNotEmpty
            ? Image.network(
                avatarUrl,
                width: 88.w,
                height: 88.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar(studentName);
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return child;
                  }
                  return _buildDefaultAvatar(studentName);
                },
              )
            : _buildDefaultAvatar(studentName),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar(String studentName) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          studentName.isNotEmpty ? studentName.substring(0, 1) : '',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建学生信息
  Widget _buildStudentInfo(InsuranceWarningRecord record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名和电话
        Row(
          children: [
            Text(
              record.studentName,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(width: 20.w),
            Icon(
              Icons.phone,
              size: 28.w,
              color: const Color(0xFF666666),
            ),
            SizedBox(width: 8.w),
            Text(
              record.phoneNumber,
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.h),
        
        // 未覆盖天数
        Text(
          '未覆盖天数：${record.uncoveredDays}天',
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFFFF4747),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusTag(InsuranceStatus status) {
    String text;
    Color backgroundColor;
    
    switch (status) {
      case InsuranceStatus.notUploaded:
        text = '未上传';
        backgroundColor = const Color(0xFFFF4747);
        break;
      case InsuranceStatus.expiringSoon:
        text = '即将到期';
        backgroundColor = const Color(0xFFFF8A00);
        break;
      case InsuranceStatus.expired:
        text = '已过期';
        backgroundColor = const Color(0xFFFF4747);
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 24.sp,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
