/// -----
/// sign_in_screen.dart
///
/// 签到页面，用于学生进行日常签到打卡
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/tag_widget.dart';
import 'package:flutter_demo/features/attendance/presentation/screens/location_setting_screen.dart';
import 'package:flutter_demo/features/attendance/presentation/widgets/sign_location_row_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../../core/widgets/custom_app_bar.dart';
import '../../../../core/widgets/course_header_section.dart';
import '../../../attendance/presentation/screens/attendance_calendar_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 签到类型枚举
enum SignInType {
  /// 单位签到
  company,
  /// 家庭签到
  home,
}

/// 签到记录模型
class SignInRecord {
  final String time;
  final String type; // '上班' 或 '下班'
  final String status; // '正常' 或 '超时'
  final String location;
  final bool isAbnormal;

  SignInRecord({
    required this.time,
    required this.type,
    required this.status,
    required this.location,
    this.isAbnormal = false,
  });
}

/// 学期信息模型
class SemesterInfo {
  final String id;
  final String name;
  final String description;

  SemesterInfo({
    required this.id,
    required this.name,
    required this.description,
  });
}

/// 签到页面
class SignInScreen extends StatefulWidget {
  const SignInScreen({Key? key}) : super(key: key);

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final TextEditingController _remarkController = TextEditingController();
  final List<File> _selectedFiles = [];
  final int _maxFiles = 20; // 20个文件上传限制

  // Mock数据
  late SemesterInfo _selectedSemester;
  late List<SemesterInfo> _semesters;
  late List<SignInRecord> _todayRecords;
  String _currentLocation = '广东省深圳市南山区德白大厦1120号...';
  bool _isSigningIn = false;

  @override
  void initState() {
    super.initState();
    _initMockData();
  }

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  /// 初始化Mock数据
  void _initMockData() {
    // 学期数据
    _semesters = [
      SemesterInfo(
        id: '1',
        name: '2021级市场销售2023-2024实习学年第二学期',
        description: '岗位实习',
      ),
      SemesterInfo(
        id: '2',
        name: '2021级市场销售2023-2024实习学年第一学期',
        description: '岗位实习',
      ),
    ];
    _selectedSemester = _semesters.first;

    // 今日签到记录
    _todayRecords = [
      SignInRecord(
        time: '08:30',
        type: '上班',
        status: '超范围',
        location: '距离地址:广东省深圳市南山区德白大厦1120号...距离500米',
        isAbnormal: true,
      ),
      SignInRecord(
        time: '08:30',
        type: '下班',
        status: '正常',
        location: '距离地址:广东省深圳市南山区德白大厦1120号...距离500米',
        isAbnormal: false,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: CustomAppBar(
        title: '签到',
        actions: [
          TextButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AttendanceCalendarScreen(),
                ),
              );
            },
            child: Text(
              '历史记录',
              style: TextStyle(
                color: const Color(0xFF2165F6),
                fontSize: 28.sp,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // 学期选择区域
          CourseHeaderSection(
            courseName: '${_selectedSemester.name} ${_selectedSemester.description}',
            availableCourses: _semesters.map((s) => '${s.name} ${s.description}').toList(),
            onCourseChanged: (newCourse) {
              // 处理学期切换逻辑
              final index = _semesters.indexWhere((s) => '${s.name} ${s.description}' == newCourse);
              if (index >= 0) {
                setState(() {
                  _selectedSemester = _semesters[index];
                });
              }
            },
          ),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 20.h),

                  // Tab切换区域
                  _buildTabSection(),

                  SizedBox(height: 20.h),

                  // 备注输入区域
                  _buildRemarkSection(),

                  SizedBox(height: 20.h),

                  // 附件上传区域
                  _buildFileUploadSection(),

                  SizedBox(height: 20.h),

                  // 位置信息区域
                  _buildLocationSection(),

                  SizedBox(height: 20.h),

                  // 签到记录列表
                  _buildSignInRecords(),

                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Tab切换区域
  Widget _buildTabSection() {
    return Container(
      height: 85.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          // 左侧"单位"区域
          Expanded(
            child: InkWell(
              onTap: () {
                // 跳转到LocationSettingScreen页面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LocationSettingScreen(locationType: '单位'),
                  ),
                );
              },
              child: Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '单位',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.normal,
                        color: const Color(0xFF2165F6),
                      ),
                    ),
                    // SizedBox(height: 8.h),
                    // 蓝色下划线
                    // Container(
                    //   width: 60.w,
                    //   height: 3.h,
                    //   color: const Color(0xFF2165F6),
                    // ),
                    Image.asset(
                      'assets/images/sign_edit_icon.png',
                      width: 24.w,
                      height: 24.w,
                    ),
                  ],
                ),
              ),
            ),
          ),
          // 中间分割线
          Container(
            width: 1.w,
            height: 40.h,
            color: const Color(0xFFE0E0E0),
          ),
          // 右侧"家"区域
          Expanded(
            child: InkWell(
              onTap: () {
                // 跳转到LocationSettingScreen页面
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LocationSettingScreen(locationType: '家'),
                  ),
                );
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '家',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.normal,
                        color: const Color(0xFF2165F6),
                      ),
                    ),

                    Image.asset(
                      'assets/images/sign_edit_icon.png',
                      width: 24.w,
                      height: 24.w,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注输入区域
  Widget _buildRemarkSection() {
    return Container(
      height: 100.h,
      color: Colors.white,
      padding: EdgeInsets.only(left: 25.w),
      child: Row(
        children: [
          Text(
            '备注',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(width: 20.w),
          Expanded(
              child: TextField(
                controller: _remarkController,
                textAlign: TextAlign.right,
                decoration: InputDecoration(
                  hintText: '请输入签到补充备注',
                  hintStyle: TextStyle(
                    fontSize: 30.sp,
                    color: const Color(0xFF999999),
                  ),
                  border: InputBorder.none, // 无边框
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  contentPadding: EdgeInsets.only(right: 25.w), // 去除内边距
                  isDense: true,
                  filled: false,
              ),
              style: TextStyle(
                  fontSize: 30.sp,
                  color: const Color(0xFF333333),
                ),
              ),
          )
        ],
      ),
    );
  }

  /// 构建附件上传区域
  Widget _buildFileUploadSection() {
    return Container(
      padding: EdgeInsets.all(25.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '附件上传',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 20.h),
          _buildFileGrid(),
        ],
      ),
    );
  }

  /// 构建文件网格
  Widget _buildFileGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      mainAxisSpacing: 20.w,
      crossAxisSpacing: 20.w,
      childAspectRatio: 1,
      children: [
        ..._selectedFiles.map((file) => _buildFileItem(file)),
        if (_selectedFiles.length < _maxFiles)
          _buildAddFileButton(),
      ],
    );
  }

  /// 构建文件项
  Widget _buildFileItem(File file) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            image: DecorationImage(
              image: FileImage(file),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -10.w,
          right: -10.w,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedFiles.remove(file);
              });
            },
            child: Container(
              padding: EdgeInsets.all(4.w),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 32.w,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建添加文件按钮
  Widget _buildAddFileButton() {
    return GestureDetector(
      onTap: _pickFile,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Center(
          child: Icon(
            Icons.add,
            size: 80.w,
            color: const Color(0xFF999999),
          ),
        ),
      ),
    );
  }

  /// 构建位置信息区域
  Widget _buildLocationSection() {
    return Container(
      padding: EdgeInsets.all(20.w),
      color: Colors.white,
      child: Column(
        children: [
          const SignLocationRowWidget(address: '当前地址:  广东省深圳市南山区福田湾大道1120号'),
          SizedBox(height: 20.h),
          Row(
            children: [
              const TagWidget(tag: '超范围'),
              SizedBox(width: 12.w),
              Text(
                '距离单位安全范围60km',
                style: TextStyle(
                  fontSize: 22.sp,
                  color: const Color(0xFFFF4747),
                ),
              )
            ],
          ),
          SizedBox(height: 50.h),
          // 签到按钮
          _buildSignInButton(),
          SizedBox(height: 40.h),
        ],
      ),
    );
  }

  /// 构建签到按钮
  Widget _buildSignInButton() {
    return Center(
      child: GestureDetector(
        onTap: _isSigningIn ? null : _performSignIn,
        child: Container(
          width: 200.w,
          height: 200.w,
          decoration: const BoxDecoration(
            color: Color(0xFF2165F6),
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (_isSigningIn)
                SizedBox(
                  width: 40.w,
                  height: 40.w,
                  child: const CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                )
              else ...[
                Text(
                  '签到',
                  style: TextStyle(
                    fontSize: 36.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '08:30',
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建签到记录列表
  Widget _buildSignInRecords() {
    return Container(
      child: Column(
        children: _todayRecords.map((record) => _buildSignInRecordItem(record)).toList(),
      ),
    );
  }

  /// 构建签到记录项
  Widget _buildSignInRecordItem(SignInRecord record) {
    return Container(
      padding: EdgeInsets.all(25.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                record.type,
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.black333,
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                record.time,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                ),
              ),
              SizedBox(width: 16.w),
              TagWidget(tag: record.status,backgroundColor: record.isAbnormal ? const Color(0xFFFF4747) : const Color(0xFF00C851),),
              if (record.isAbnormal) ...[
                SizedBox(width: 16.w),
                Text(
                  '偏移单位安全范围60km',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: const Color(0xFFFF4747),
                  ),
                ),
              ]
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            '签到地址：${_currentLocation}',
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            record.location,
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 选择文件
  Future<void> _pickFile() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _selectedFiles.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      debugPrint('Error picking file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择文件失败，请重试')),
      );
    }
  }

  /// 调整位置
  void _adjustLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('位置调整功能开发中')),
    );
  }

  /// 执行签到
  Future<void> _performSignIn() async {
    setState(() {
      _isSigningIn = true;
    });

    // 模拟签到过程
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isSigningIn = false;
    });

    // 显示签到成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('签到成功！')),
    );

    // 更新签到记录
    setState(() {
      _todayRecords.insert(0, SignInRecord(
        time: '${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}',
        type: '上班',
        status: '正常',
        location: '距离地址:$_currentLocation距离0米',
        isAbnormal: false,
      ));
    });
  }
}
