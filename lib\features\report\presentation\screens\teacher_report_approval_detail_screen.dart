/// -----
/// teacher_report_approval_detail_screen.dart
///
/// 教师端报告审核详情页面，用于展示学生提交的报告详情并进行批阅
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';
import 'package:flutter_demo/features/report/data/models/weekly_report.dart';
import 'package:flutter_demo/features/report/presentation/widgets/student_info_card.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class TeacherReportApprovalDetailScreen extends StatefulWidget {
  final BaseReport report;
  final ReportType reportType;

  const TeacherReportApprovalDetailScreen({
    Key? key,
    required this.report,
    required this.reportType,
  }) : super(key: key);

  @override
  State<TeacherReportApprovalDetailScreen> createState() =>
      _TeacherReportApprovalDetailScreenState();
}

class _TeacherReportApprovalDetailScreenState
    extends State<TeacherReportApprovalDetailScreen> {
  // 控制器用于编辑AI评阅内容
  final TextEditingController _aiReviewController = TextEditingController();

  // 是否处于编辑模式
  bool _isEditing = false;

  @override
  void dispose() {
    _aiReviewController.dispose();
    super.dispose();
  }
  // 使用AI评阅
  void _useAIReview() {
    // 显示底部弹出对话框
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxHeight: 847.h, // 设置最大高度
      ),
      builder: (BuildContext context) {
        return _buildAIReviewBottomSheet();
      },
    );
  }

  // 构建AI评阅底部弹出对话框
  Widget _buildAIReviewBottomSheet() {
    // AI评阅内容
    String aiReviewContent = '写的不错，任然有改进的位置，可以把周报写的更加详细点，可以包含你解决了什么问题，遇到了什么困难，下一步的计划，以及预期达到的目标。';

    // 设置初始文本
    _aiReviewController.text = aiReviewContent;

    // 重置编辑模式
    _isEditing = false;

    // 创建一个StatefulBuilder，以便在底部弹出框内管理状态
    return StatefulBuilder(
      builder: (BuildContext context, StateSetter setState) {
        // 评分星级（3.5星）
        double rating = 3.5;

        return Container(
      height: 847.h, // 设置固定高度为847px
      padding: EdgeInsets.only(top: 20.h),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        children: [
          // 顶部内容区域（带背景图）
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                image: const DecorationImage(
                  image: AssetImage('assets/images/ai_review_dialog_bg.png'),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // AI机器人图标（包含标题和副标题）- 居左对齐，距离顶部36px
                    Container(
                      height: 180.h, // 设置足够的高度以容纳图片和关闭按钮
                      child: Stack(
                        children: [
                          Positioned(
                            left: 20.w,
                            top: 36.h,
                            child: Container(
                              alignment: Alignment.centerLeft,
                              child: Image.asset(
                                'assets/images/ai_review_bot_icon.png',
                                width: 560.w,
                                height: 139.h,
                                alignment: Alignment.centerLeft,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 59.h,
                            right: 41.w,
                            child: GestureDetector(
                              onTap: () => Navigator.pop(context),
                              child: Icon(
                                Icons.close,
                                size: 40.r,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 20.h),

                    // 白色背景容器，包含评星区域和AI评阅内容
                    Container(
                      width: double.infinity,
                      height: 510.h,
                      margin: EdgeInsets.symmetric(horizontal: 20.w),
                      padding: EdgeInsets.all(20.r),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Column(
                        children: [
                          // 评星区域 - 背景为#F8F8F8，圆角10px，垂直padding 39px，左padding 30px
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.only(left: 30.w, top: 39.h, bottom: 39.h, right: 20.w),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F8F8),
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  '评星',
                                  style: TextStyle(
                                    fontSize: 26.sp,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                SizedBox(width: 20.w),
                                // 星星评分 - 星星之间间距40px
                                Row(
                                  children: List.generate(5, (index) {
                                    // 创建一个包含星星和间距的容器
                                    return Padding(
                                      // 除了最后一个星星，其他都添加右边距
                                      padding: EdgeInsets.only(right: index < 4 ? 40.w : 0),
                                      child: index < rating.floor()
                                          // 完整星星
                                          ? Icon(Icons.star, color: Colors.amber, size: 40.r)
                                          // 半星
                                          : (index == rating.floor() && rating % 1 != 0)
                                              ? Icon(Icons.star_half, color: Colors.amber, size: 40.r)
                                              // 空星
                                              : Icon(Icons.star_border, color: Colors.amber, size: 40.r),
                                    );
                                  }),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(height: 20.h),

                          // AI评阅内容区域 - 背景为#F8F8F8，圆角10px，左右padding 30px，上下padding 32px
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 32.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F8F8),
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: _isEditing
                                ? TextField(
                                    controller: _aiReviewController,
                                    maxLines: 6,
                                    autofocus: true, // 自动获取焦点
                                    style: TextStyle(
                                      fontSize: 26.sp,
                                      color: Colors.black87,
                                      height: 1.5,
                                    ),
                                    decoration: InputDecoration(
                                      border: InputBorder.none,
                                      hintText: '请输入评阅内容',
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                  )
                                : Text(
                                    _aiReviewController.text,
                                    style: TextStyle(
                                      fontSize: 26.sp,
                                      color: Colors.black87,
                                      height: 1.5,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            ),
          ),

          // 底部按钮区域
          Container(
            color: Colors.white,
            height: 128.h, // 设置底部按钮区域高度
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
            child: Row(
              children: [
                // 编辑/取消按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      if (_isEditing) {
                        // 如果正在编辑，点击取消编辑
                        setState(() {
                          _isEditing = false;
                          // 恢复原始内容
                          _aiReviewController.text = aiReviewContent;
                        });
                      } else {
                        // 如果不在编辑，进入编辑模式
                        setState(() {
                          _isEditing = true;
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppTheme.primaryColor,
                      minimumSize: Size(0, 88.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                        side: BorderSide(color: AppTheme.primaryColor),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      _isEditing ? '取消' : '编辑',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 20.w),
                // 提交按钮
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // 如果在编辑模式，保存编辑内容
                      if (_isEditing) {
                        setState(() {
                          _isEditing = false;
                        });
                      }

                      Navigator.pop(context);
                      // 显示绿色背景的SnackBar
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('评阅已提交'),
                          backgroundColor: Colors.green, // 设置绿色背景
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      minimumSize: Size(0, 88.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      '提交',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
      },
    );
  }

  // 获取AppBar标题
  String _getAppBarTitle() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '学生日报详情';
      case ReportType.weekly:
        return '学生周报详情';
      case ReportType.monthly:
        return '学生月报详情';
      case ReportType.summary:
        return '学生实习总结详情';
    }
  }

  // 获取报告周期信息
  String _getReportPeriodInfo() {
    switch (widget.reportType) {
      case ReportType.daily:
        return '日报 ${DateFormat('yyyy.MM.dd').format(
            widget.report.createdAt)}';
      case ReportType.weekly:
        final weeklyReport = widget.report as WeeklyReport;
        return '第三周周报 ${DateFormat('yyyy.MM.dd').format(
            weeklyReport.startDate)}-${DateFormat('yyyy.MM.dd').format(
            weeklyReport.endDate)}';
      case ReportType.monthly:
        return '第三月月报 2025.05.15-2025.05.20';
      case ReportType.summary:
        return '实习总结 2025.05.15-2025.05.20';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: CustomAppBar(
        title: _getAppBarTitle(),
        backgroundColor: Colors.white,
        showBackButton: true,
      ),
      // 使用bottomNavigationBar确保按钮固定在底部
      bottomNavigationBar: _buildBottomButton(),
      body: Column(
        children: [
          // 课程头部 - 使用普通文本显示
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
            color: Colors.white,
            child: Text(
              widget.report.courseName,
              style: TextStyle(
                fontSize: 28.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 内容区域
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 学生信息卡片
                      _buildStudentInfoCard(),

                      // 报告内容区域
                      _buildReportContentCard(),

                      // 底部安全间距
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建学生信息卡片
  Widget _buildStudentInfoCard() {
    return StudentInfoCard(
      userName: '李成儒',
      company: '武汉诚通信息技术有限公司',
      status: widget.report.status,
      position: '程序员',
      margin: EdgeInsets.zero,
      padding: EdgeInsets.all(16.r),
      avatarRadius: 88.r,
    );
  }

  // 构建报告内容区域
  Widget _buildReportContentCard() {
    return Padding(
      padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 报告标题
          Text(
            _getReportPeriodInfo(),
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 39.h),

          // 问题1
          _buildQuestionAnswer(
              '问题一：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 问题2
          _buildQuestionAnswer(
              '问题二：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 问题3
          _buildQuestionAnswer(
              '问题三：已完成用户个人中心UI优化，已全量上线？',
              '修复订单状态同步延迟BUG，测试通过率100%'
          ),
          SizedBox(height: 24.h),

          // 提交时间
          Text(
            '提交时间：2025-04-23 22:12',
            style: TextStyle(
              fontSize: 24.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 构建问题和回答
  Widget _buildQuestionAnswer(String question, String answer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题文本
        Text(
          question,
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.black666,
            height: 1.4,
          ),
        ),
        SizedBox(height: 16.h),

        // 回答内容 - 使用灰色背景
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 28.h),
          decoration: BoxDecoration(
            color: const Color(0xFFF7F7F7),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '答：',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                  height: 1.4,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  answer,
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建底部按钮
  Widget _buildBottomButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _useAIReview,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          minimumSize: Size(double.infinity, 88.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 12.h),
        ),
        child: Text(
          'AI评阅',
          style: TextStyle(
            fontSize: 30.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}