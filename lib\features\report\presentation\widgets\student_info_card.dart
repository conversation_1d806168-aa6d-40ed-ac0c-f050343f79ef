/// -----
/// student_info_card.dart
///
/// 学生信息卡片组件，用于展示学生的基本信息，包括头像、姓名、职位、公司等
/// 在报告列表和报告详情页面中复用
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class StudentInfoCard extends StatelessWidget {
  /// 学生姓名
  final String userName;

  /// 学生职位/标签
  final String position;

  /// 学生公司
  final String company;

  /// 报告状态
  final ReportStatus status;

  /// 头像大小
  final double avatarRadius;

  /// 卡片内边距
  final EdgeInsetsGeometry padding;

  /// 卡片外边距
  final EdgeInsetsGeometry margin;

  /// 卡片点击事件
  final VoidCallback? onTap;

  /// 自定义头像URL
  final String? avatarUrl;

  const StudentInfoCard({
    Key? key,
    required this.userName,
    this.position = '程序员',
    required this.company,
    required this.status,
    this.avatarRadius = 88,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(16),
    this.onTap,
    this.avatarUrl = AppConstants.avatar1,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取状态文本和颜色
    String statusText = status == ReportStatus.submitted ? '待批阅' :
                        (status == ReportStatus.approved ? '已批阅' : '已驳回');

    return Container(
      margin: margin,
      padding: padding,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: InkWell(
        onTap: onTap,
        child: Column(
          children: [
            Stack(
              children: [
                // 主要内容
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 学生头像
                    CircleAvatar(
                      radius: avatarRadius.r,
                      backgroundColor: Colors.grey[200],
                      backgroundImage: NetworkImage(avatarUrl ?? AppConstants.avatar1),
                    ),
                    SizedBox(width: 12.w),

                    // 学生信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                userName,
                                style: TextStyle(
                                  fontSize: 28.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(width: 8.w),
                              // 职位标签
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4.r),
                                  border: Border.all(color: Colors.blue, width: 0.5),
                                ),
                                child: Text(
                                  position,
                                  style: TextStyle(
                                    fontSize: 20.sp,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16.h),
                          // 公司名称
                          Text(
                            company,
                            style: TextStyle(
                              fontSize: 24.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 为状态标签预留空间
                    SizedBox(width: 100.w),
                  ],
                ),

                // 状态标签 - 使用Positioned确保垂直居中和右边距
                Positioned(
                  right: 29.w,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Text(
                      statusText,
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: status == ReportStatus.submitted ? AppTheme.blue2165f6 : Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            // 分隔线
            Divider(height: 1, thickness: 0.5, color: Colors.grey[200]),
          ],
        ),
      ),
    );
  }
}
