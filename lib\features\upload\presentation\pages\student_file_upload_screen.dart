/// -----
/// student_file_upload_page.dart
///
/// 学生端文件上传页面，实现文件上传列表、学年学期切换等功能，UI风格参考设计图。
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/file_list_item.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';
import 'package:flutter_demo/core/widgets/empty_state_widget.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import '../bloc/file_upload_bloc.dart';
import '../bloc/file_upload_event.dart';
import '../bloc/file_upload_state.dart';
import '../../domain/entities/file_requirement.dart';

class StudentFileUploadScreen extends StatelessWidget {
  const StudentFileUploadScreen({
    Key? key,
    this.planId = '8',
  }) : super(key: key);

  /// 计划ID，默认使用测试数据
  final String planId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => GetIt.instance<FileUploadBloc>()
        ..add(LoadFileRequirementsEvent(planId: planId)),
      child: const StudentFileUploadView(),
    );
  }
}

class StudentFileUploadView extends StatelessWidget {
  const StudentFileUploadView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: const CustomAppBar(title: '文件上传'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学年学期信息
          const CourseHeaderSection(courseName: '2021级市场销售2023-2024实习学年第二学期岗位实习'),
          // 文件上传列表
          Expanded(
            child: BlocConsumer<FileUploadBloc, FileUploadState>(
              listener: (context, state) {
                if (state is FileUploadRefreshFailure) {
                  AppSnackBar.showError(context, state.message);
                }
              },
              builder: (context, state) {
                if (state is FileUploadInitial ||
                    (state is FileUploadLoading && state.isFirstFetch)) {
                  return const LoadingWidget();
                }

                if (state is FileUploadFailure && state.fileRequirements.isEmpty) {
                  return EmptyStateWidget(
                    icon: Icons.error_outline,
                    title: '加载失败',
                    message: state.message,
                    buttonText: '重试',
                    onButtonPressed: () => _refreshData(context),
                  );
                }

                final fileRequirements = _getFileRequirements(state);

                if (fileRequirements.isEmpty) {
                  return EmptyStateWidget(
                    icon: Icons.inbox,
                    title: '暂无文件要求',
                    message: '当前没有需要上传的文件',
                    buttonText: '刷新',
                    onButtonPressed: () => _refreshData(context),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () => _refreshData(context),
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 25.w),
                    itemCount: fileRequirements.length,
                    itemBuilder: (context, index) {
                      final fileRequirement = fileRequirements[index];
                      return _buildFileListItem(context, fileRequirement);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 获取文件要求列表
  List<FileRequirement> _getFileRequirements(FileUploadState state) {
    if (state is FileUploadSuccess) {
      return state.fileRequirements;
    } else if (state is FileUploadFailure) {
      return state.fileRequirements;
    }
    return [];
  }

  /// 刷新数据
  Future<void> _refreshData(BuildContext context) async {
    context.read<FileUploadBloc>().add(
      const RefreshFileRequirementsEvent(planId: '8'),
    );
  }

  /// 构建文件列表项
  Widget _buildFileListItem(BuildContext context, FileRequirement fileRequirement) {
    return FileListItem.withStatus(
      fileId: fileRequirement.id ?? fileRequirement.fileCode.toString(),
      iconPath: fileRequirement.iconPath,
      fileName: fileRequirement.fileType,
      statusText: fileRequirement.fileStatus.statusText,
      statusBackgroundColor: Color(fileRequirement.fileStatus.statusBackgroundColor),
      statusTextColor: Color(fileRequirement.fileStatus.statusTextColor),
      onTap: () => _navigateToDetail(context, fileRequirement),
    );
  }

  /// 导航到详情页面
  static Future<void> _navigateToDetail(BuildContext context, FileRequirement fileRequirement) async {
    // 跳转到详情页面，传递完整的文件要求对象，并监听返回结果
    final shouldRefresh = await context.push(
      '/student_file_upload_detail/${fileRequirement.id ?? fileRequirement.fileCode.toString()}',
      extra: fileRequirement,
    );

    // 如果返回结果为true，则刷新数据
    if (shouldRefresh == true && context.mounted) {
      context.read<FileUploadBloc>().add(
        const RefreshFileRequirementsEvent(planId: '8'),
      );
    }
  }
}

