/// -----
/// checkin_statistics_screen.dart
/// 
/// 学生签到统计页面，用于展示学生签到统计数据和学生列表
/// 
/// 功能：
/// 1. 展示签到统计数据（未签到、已签到等）
/// 2. 提供未签到/已签到的分类查看
/// 3. 展示学生列表及其状态，支持提醒功能
/// 4. 支持时间范围选择和一键提醒功能
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/sticky_tab_bar.dart';
import 'package:flutter_demo/core/widgets/student_list_item.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 签到学生数据模型
class CheckinStudentModel implements StudentInfo {
  @override
  final String id;
  @override
  final String name;
  @override
  final String phone;
  @override
  final String avatar;
  @override
  final String status; // '待实习', '实习中'
  final bool hasCheckedIn; // 是否已签到

  CheckinStudentModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.avatar,
    required this.status,
    required this.hasCheckedIn,
  });

  /// 获取示例数据
  static List<CheckinStudentModel> getSampleData() {
    return [
      CheckinStudentModel(
        id: '1',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '待实习',
        hasCheckedIn: false,
      ),
      CheckinStudentModel(
        id: '2',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasCheckedIn: false,
      ),
      CheckinStudentModel(
        id: '3',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasCheckedIn: false,
      ),
      CheckinStudentModel(
        id: '4',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasCheckedIn: false,
      ),
      CheckinStudentModel(
        id: '5',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasCheckedIn: true,
      ),
      CheckinStudentModel(
        id: '6',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        status: '实习中',
        hasCheckedIn: true,
      ),
    ];
  }
}

/// 学生签到统计页面
class CheckinStatisticsScreen extends StatefulWidget {
  const CheckinStatisticsScreen({Key? key}) : super(key: key);

  @override
  State<CheckinStatisticsScreen> createState() => _CheckinStatisticsScreenState();
}

class _CheckinStatisticsScreenState extends State<CheckinStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';
  final String _currentTime = '今日';
  late List<CheckinStudentModel> _allStudents;
  late List<CheckinStudentModel> _uncheckedStudents;
  late List<CheckinStudentModel> _checkedStudents;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载数据
  void _loadData() {
    _allStudents = CheckinStudentModel.getSampleData();
    // 模拟数据分类
    _uncheckedStudents = _allStudents.where((s) => !s.hasCheckedIn).toList();
    _checkedStudents = _allStudents.where((s) => s.hasCheckedIn).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: CustomAppBar(
        title: '签到情况',
        actions: [
          // 时间选择器
          Container(
            margin: EdgeInsets.only(right: 32.w),
            child: GestureDetector(
              onTap: _showTimeSelector,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _currentTime,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: AppTheme.black333,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    size: 32.w,
                    color: AppTheme.black666,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // 课程头部
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // 课程头部 - 自动从全局状态获取实习计划数据
                  const CourseHeaderSection(),
                  // 分割线
                  Container(
                    height: 1.h,
                    color: AppTheme.dividerColor,
                    margin: EdgeInsets.symmetric(horizontal: 40.w),
                  ),
                ],
              ),
            ),
            // 固定的Tab栏
            SliverPersistentHeader(
              pinned: true,
              delegate: StickyTabBarDelegate(
                tabBar: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.blue2165f6,
                  unselectedLabelColor: AppTheme.black666,
                  indicatorColor: AppTheme.blue2165f6,
                  indicatorWeight: 3,
                  labelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  tabs: [
                    CountedTab(
                      text: '未签到',
                      count: _uncheckedStudents.length,
                      showCountAsBadge: true,
                    ),
                    const Tab(text: '已签到'),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildStudentList(_uncheckedStudents),
            _buildStudentList(_checkedStudents),
          ],
        ),
      ),
      // 底部一键提醒按钮
      bottomNavigationBar: _buildBottomActionBar(),
      // 签到明细浮动按钮
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 显示时间选择器
  void _showTimeSelector() {
    // TODO: 实现时间选择功能
    debugPrint('显示时间选择器');
  }

  /// 构建学生列表
  Widget _buildStudentList(List<CheckinStudentModel> students) {
    if (students.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 120.w,
              color: AppTheme.black999,
            ),
            SizedBox(height: 32.h),
            Text(
              '暂无数据',
              style: TextStyle(
                fontSize: 28.sp,
                color: AppTheme.black999,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
      itemCount: students.length,
      itemBuilder: (context, index) {
        final student = students[index];
        return StudentListItem(
          student: student,
          mode: StudentListItemMode.checkin,
          actionButtonText: !student.hasCheckedIn ? '去提醒' : null,
          showActionButton: !student.hasCheckedIn,
          onActionButtonTap: () => _showReminder(student),
        );
      },
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(40.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: ElevatedButton(
        onPressed: _batchReminder,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.blue2165f6,
          padding: EdgeInsets.symmetric(vertical: 24.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '一键提醒',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showCheckinDetails,
      backgroundColor: AppTheme.blue2165f6,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment,
            color: Colors.white,
            size: 24.w,
          ),
          Text(
            '签到\n明细',
            style: TextStyle(
              fontSize: 20.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 显示提醒功能
  void _showReminder(CheckinStudentModel student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提醒学生'),
        content: Text('确定要提醒 ${student.name} 进行签到吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendReminder(student);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 发送提醒
  void _sendReminder(CheckinStudentModel student) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已向 ${student.name} 发送签到提醒'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 批量提醒
  void _batchReminder() {
    if (_uncheckedStudents.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('当前没有需要提醒的学生'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量提醒'),
        content: Text('确定要向 ${_uncheckedStudents.length} 名未签到学生发送提醒吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendBatchReminder();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 发送批量提醒
  void _sendBatchReminder() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已向 ${_uncheckedStudents.length} 名学生发送签到提醒'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示签到明细
  void _showCheckinDetails() {
    // TODO: 实现签到明细功能
    debugPrint('显示签到明细');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('签到明细功能开发中'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
