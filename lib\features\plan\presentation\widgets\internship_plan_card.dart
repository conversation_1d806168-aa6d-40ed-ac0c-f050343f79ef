/// -----
/// internship_plan_card.dart
/// 
/// 实习计划卡片组件，用于显示实习计划信息
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/plan/data/models/internship_plan_info.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 实习计划卡片组件
///
/// 用于显示实习计划信息，包括标题、状态、详情和操作按钮
class InternshipPlanCard extends StatelessWidget {
  /// 实习计划信息
  final InternshipPlanInfo plan;
  
  /// 点击卡片回调
  final VoidCallback? onTap;
  
  /// 申请实习回调
  final Function(String planId)? onApply;
  
  /// 申请免实习回调
  final Function(String planId)? onExempt;

  const InternshipPlanCard({
    Key? key,
    required this.plan,
    this.onTap,
    this.onApply,
    this.onExempt,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(left: 25.w,right: 25.w,top: 20.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题部分，带有状态标签
            _buildPlanHeader(),
            Container(
              height: 1.h,      // 分割线高度
              color: const Color(0xFFC5C5C5), // 灰色
            ),
            // 详情部分
            _buildPlanDetails(),

            // 底部按钮部分
            _buildPlanActions(context),
          ],
        ),
      ),
    );
  }

  /// 构建计划标题和状态
  Widget _buildPlanHeader() {
    return Container(
      padding: EdgeInsets.all(30.h),
      child: RichText(
        text: TextSpan(
          children: [
            // 标题
            TextSpan(
              text: plan.title,
              style: TextStyle(
                color: AppTheme.black333,
                fontSize: 30.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            // 空白间隔
            TextSpan(text: ' '),
            // 状态标签
            WidgetSpan(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: _getStatusColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  plan.status,
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: _getStatusColor(),
                  ),
                ),
              ),
              alignment: PlaceholderAlignment.middle,
            ),
          ],
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建计划详情
  Widget _buildPlanDetails() {
    return Padding(
      padding: EdgeInsets.only(left: 30.w,right: 30.w, top: 30.h),
      child: Column(
        children: [
          _buildDetailRow('实习类型', plan.type),
          SizedBox(height: 20.h),
          _buildDetailRow('实习周期', plan.period),
          SizedBox(height: 20.h),
          _buildDetailRow('实习学期', plan.semester),
          SizedBox(height: 20.h),
          _buildDetailRow('创建人', plan.creator),
        ],
      ),
    );
  }

  /// 构建详情行
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black999
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 24.sp,
              color:  AppTheme.black333,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildPlanActions(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(30.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (plan.canExempt)
            SizedBox(
              width: 160.w,
              child: OutlinedButton(
                onPressed: () {
                  if (onExempt != null) {
                    onExempt!(plan.id);
                  }
                },
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey[400]!,width: 1.w),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                ),
                child: Text(
                  '免实习',
                  style: TextStyle(
                    color: AppTheme.black666,
                    fontSize: 26.sp,
                  ),
                ),
              ),
            ),
          SizedBox(width: 12.w),
          if (plan.canApply)
            SizedBox(
              width: 160.w,
              child: OutlinedButton(
                onPressed: () {
                  if (onApply != null) {
                    onApply!(plan.id);
                  }
                },
                style: OutlinedButton.styleFrom(
                  backgroundColor: Colors.white,
                  side:  BorderSide(
                    color: Colors.blue,  // Blue border color
                    width: 1.w,           // 1px border width
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  elevation: 0,
                ),
                child: Text(
                  '实习申请',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 26.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (plan.status) {
      case '进行中':
        return AppTheme.primaryColor;
      case '已结束':
        return Colors.grey;
      case '未开始':
        return Colors.orange;
      default:
        return AppTheme.primaryColor;
    }
  }
}
