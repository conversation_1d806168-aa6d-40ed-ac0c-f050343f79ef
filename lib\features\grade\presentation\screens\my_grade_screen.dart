/// -----
/// my_grade_screen.dart
///
/// 我的成绩页面，展示学生实习课程的成绩信息，包括总分、学校评分和企业评分
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/router/route_constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_circle_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 我的成绩页面
///
/// 展示学生的实习成绩信息，包括总分和各项评分详情
class MyGradeScreen extends StatefulWidget {
  const MyGradeScreen({Key? key}) : super(key: key);

  @override
  State<MyGradeScreen> createState() => _MyGradeScreenState();
}

class _MyGradeScreenState extends State<MyGradeScreen> {
  /// 当前选中的课程
  String _currentCourse = '2021级市场销售2023-2024实习学年第二学期岗位实习';



  /// 成绩数据
  final Map<String, dynamic> _gradeData = {
    'totalScore': 84,
    'selfScore': 86,
    'teacherScore': 72,
    'companyScore': null, // null表示未评分
  };

  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final double statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '我的成绩',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      extendBodyBehindAppBar: true, // 让AppBar透明
      body: Stack(
        children: [
          // 顶部背景图片
          Positioned(
            child: Image.asset(
              'assets/images/internship_score_detail_top_bg.png',
              height: 628.h,
              fit: BoxFit.fitHeight,
              errorBuilder: (context, error, stackTrace) {
                // 如果图片加载失败，使用渐变背景
                return Container(
                  height: 628.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF4A90E2),
                        Color(0xFF7BB3F0),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // 内容区域
          Column(
            children: [
              // 课程头部 - 透明背景，白色文字
              Container(
                margin: EdgeInsets.only(
                  top: statusBarHeight + kToolbarHeight, // 状态栏高度 + AppBar高度
                  left: 24.w,
                  right: 24.w,
                ),
                child: Text(
                  _currentCourse,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    left: 25.w,
                    right: 25.w,
                    top: 35.h,
                  ),
                  child: Column(
                    children: [
                      // 总分圆环
                      _buildScoreCircle(),
                      SizedBox(height: 20.h),

                      // 成绩列表
                      _buildGradeListSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  /// 构建评分圆环
  Widget _buildScoreCircle() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 46.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          ScoreCircleWidget(
            score: _gradeData['totalScore'] ?? 0,
            title: '实习总分',
            size: 263,
            strokeWidth: 20,
            scoreTextStyle: TextStyle(
              fontSize: 113.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            titleTextStyle: TextStyle(
              fontSize: 22.sp,
              color: AppTheme.black999,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建成绩列表区域
  Widget _buildGradeListSection() {
    return Container(

      child: Column(
        children: [
          // 自我评分
          _buildGradeItem(
            title: '自我评分',
            score: _gradeData['selfScore'],
            scoreColor: AppTheme.primaryColor,
            onTap: () => _handleGradeItemTap('self'),
          ),

          SizedBox(height: 20.h),

          // 校内老师评分
          _buildGradeItem(
            title: '校内老师评分',
            score: _gradeData['teacherScore'],
            scoreColor: const Color(0xFF00C17F),
            onTap: () => _handleGradeItemTap('teacher'),
          ),

          SizedBox(height: 20.h),

          // 企业综合评分
          _buildGradeItem(
            title: '企业综合评分',
            score: _gradeData['companyScore'],
            scoreColor: AppTheme.black999,
            placeholder: '暂未评分',
            onTap: () => _handleGradeItemTap('company'),
          ),

          SizedBox(height: 20.h),

          // 邀请企业评分按钮
          _buildInviteButton(),

          SizedBox(height: 40.h),
        ],
      ),
    );
  }

  /// 构建成绩项
  Widget _buildGradeItem({
    required String title,
    required Color scoreColor,
    int? score,
    String? placeholder,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 120.h,
        padding: EdgeInsets.symmetric(horizontal: 25.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          children: [
            // 标题
            Text(
              title,
              style: TextStyle(
                fontSize: 32.sp,
                color: AppTheme.black333,
              ),
            ),

            const Spacer(),

            // 分数或占位符
            if (score != null)
              Text(
                score.toString(),
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                  color: scoreColor,
                ),
              )
            else if (placeholder != null)
              Text(
                placeholder,
                style: TextStyle(
                  fontSize: 32.sp,
                  color: scoreColor,
                ),
              ),

            SizedBox(width: 16.w),

            // 右箭头
            Icon(
              Icons.arrow_forward_ios,
              size: 24.sp,
              color: AppTheme.black999,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建分割线
  Widget _buildDivider() {
    return Divider(
      height: 1.h,
      thickness: 1.h,
      color: const Color(0xFFF0F0F0),
    );
  }

  /// 构建邀请企业评分按钮
  Widget _buildInviteButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: InkWell(
        onTap: _handleInviteCompanyEvaluation,
        child: Text(
          '邀请企业评分',
          style: TextStyle(
            fontSize: 28.sp,
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.none,
          ),
        ),
      ),
    );
  }



  /// 处理成绩项点击
  void _handleGradeItemTap(String type) {
    String message;
    switch (type) {
      case 'self':
        message = '查看自我评分详情';
        break;
      case 'teacher':
        message = '查看校内老师评分详情';
        break;
      case 'company':
        message = '查看企业综合评分详情';
        break;
      default:
        message = '查看评分详情';
    }

    AppSnackBar.show(context, message);
  }

  /// 处理邀请企业评分
  void _handleInviteCompanyEvaluation() {
    context.push(AppRoutes.studentEnterpriseEvaluation);
  }
}

