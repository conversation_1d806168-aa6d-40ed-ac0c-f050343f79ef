/// -----
/// form_field_item.dart
///
/// 通用表单项组件，用于显示标签和值的组合，支持不同类型的输入方式和交互
///
/// <AUTHOR>
/// @date 2025-05-22
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 表单项类型枚举
enum FormFieldType {
  /// 纯文本显示
  text,

  /// 文本输入
  input,

  /// 下拉选择
  dropdown,

  /// 日期选择
  date,

  /// 地址选择
  location,
}

/// 通用表单项组件
///
/// 用于统一展示标签和值的组合，支持多种交互方式
/// 可以根据类型展示不同的右侧图标和交互方式
class FormFieldItem extends StatefulWidget {
  /// 表单项标签
  final String label;

  /// 表单项当前值
  final String value;

  /// 表单项类型
  final FormFieldType type;

  /// 是否显示右侧箭头
  final bool showArrow;

  /// 是否显示分隔线
  final bool showDivider;

  /// 点击事件回调
  final VoidCallback? onTap;

  /// 值变化回调
  final ValueChanged<String>? onChanged;

  /// 提示文本
  final String? hintText;

  /// 下拉选项列表（仅在type为dropdown时有效）
  final List<String>? dropdownItems;

  /// 创建表单项组件
  const FormFieldItem({
    Key? key,
    required this.label,
    this.value = '',
    this.type = FormFieldType.text,
    this.showArrow = true,
    this.showDivider = true,
    this.onTap,
    this.onChanged,
    this.hintText,
    this.dropdownItems,
  }) : super(key: key);

  @override
  _FormFieldItemState createState() => _FormFieldItemState();
}

class _FormFieldItemState extends State<FormFieldItem> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.value);
  }

  @override
  void didUpdateWidget(FormFieldItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value && _textController.text != widget.value) {
      _textController.text = widget.value;
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: widget.onTap,
          child: Container(
            height: 100.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            color: Colors.white,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 左侧标签
                SizedBox(
                  width: 260.w,
                  child: Text(
                    widget.label,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: AppTheme.black333,
                    ),
                  ),
                ),

                // 右侧内容
                Expanded(
                  child: _buildValueWidget(),
                ),

                // 右侧箭头（如果需要）
                if (widget.showArrow && widget.type != FormFieldType.input)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.sp,
                    color: Colors.grey,
                  ),
              ],
            ),
          ),
        ),

        // 底部分隔线（如果需要）
        if (widget.showDivider)
          Divider(
            height: 1.h,
            thickness: 1.h,
            indent: 16.w,
            color: AppTheme.dividerColor,
          ),
      ],
    );
  }

  /// 构建值显示部分的Widget
  Widget _buildValueWidget() {
    switch (widget.type) {
      case FormFieldType.input:
        return TextField(
          controller: _textController,
          style: TextStyle(
            fontSize: 28.sp,
            color: AppTheme.black333,
          ),
          textAlign: TextAlign.right,
          decoration: InputDecoration(
            hintText: widget.hintText ?? '请输入${widget.label}',
            hintStyle: TextStyle(
              fontSize: 28.sp,
              color: AppTheme.black999,
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            isDense: true,
            filled: false,
            contentPadding: EdgeInsets.zero,
          ),
          onChanged: (value) {
            if (widget.onChanged != null) {
              widget.onChanged!(value);
            }
          },
        );

      case FormFieldType.dropdown:
        return Text(
          widget.value.isEmpty ? (widget.hintText ?? '请选择${widget.label}') : widget.value,
          style: TextStyle(
            fontSize: 30.sp,
            color: widget.value.isEmpty ? AppTheme.textHintColor : AppTheme.black333,
          ),
          textAlign: TextAlign.right,
        );

      case FormFieldType.date:
        return Text(
          widget.value.isEmpty ? (widget.hintText ?? '请选择日期') : widget.value,
          style: TextStyle(
            fontSize: 30.sp,
            color: widget.value.isEmpty ? AppTheme.textHintColor : AppTheme.black333,
          ),
          textAlign: TextAlign.right,
        );

      case FormFieldType.location:
        return Text(
          widget.value.isEmpty ? (widget.hintText ?? '请选择地址') : widget.value,
          style: TextStyle(
            fontSize: 30.sp,
            color: widget.value.isEmpty ? AppTheme.textHintColor : AppTheme.black333,
          ),
          textAlign: TextAlign.right,
        );

      case FormFieldType.text:
      default:
        return Text(
          widget.value,
          style: TextStyle(
            fontSize: 30.sp,
            color: AppTheme.black333,
          ),
          textAlign: TextAlign.right,
        );
    }
  }
}
