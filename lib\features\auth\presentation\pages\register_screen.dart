import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import '../../../../core/widgets/app_snack_bar.dart';
import '../../../../core/widgets/immersive_app_bar.dart';
import '../../../../core/config/injection/injection.dart';
import '../../../../core/router/app_navigator.dart';
import '../bloc/register/register_bloc.dart';
import '../bloc/register/register_event.dart';
import '../bloc/register/register_state.dart';
import '../widgets/register_form.dart';

/// 注册页面
///
/// 显示注册表单，处理注册逻辑
class RegisterScreen extends StatelessWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<RegisterBloc>(),
      child: const RegisterView(),
    );
  }
}

/// 注册视图
///
/// 注册页面的内容部分
class RegisterView extends StatelessWidget {
  const RegisterView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 使用 AnnotatedRegion 设置状态栏样式，比 SystemChrome.setSystemUIOverlayStyle 更可靠
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // 状态栏背景透明
        statusBarIconBrightness: Brightness.dark, // 状态栏图标为深色
        statusBarBrightness: Brightness.light, // iOS状态栏亮度
      ),
      child: PopScope(
        canPop: false,
        onPopInvoked: (didPop) {
          if (!didPop) {
            AppNavigator.goToLogin(context);
          }
        },
        child: Scaffold(
          // 确保Scaffold不会为AppBar预留空间
          extendBodyBehindAppBar: true,
          // 使用沉浸式AppBar
          appBar: ImmersiveAppBar(
            onBackPressed: () => AppNavigator.goToLogin(context),
          ),
          // 使用Stack作为主体布局
          body: Stack(
            children: [
              // 背景图层 - 覆盖整个屏幕包括状态栏
              Positioned.fill(
                child: Image.asset(
                  'assets/images/register_top_bg.png',
                  fit: BoxFit.cover,
                  alignment: Alignment.topCenter,
                ),
              ),

              // 内容图层
              BlocConsumer<RegisterBloc, RegisterState>(
                listener: (context, state) {
                  if (state is RegisterSuccessState) {
                    // 注册成功，显示成功消息并返回登录页
                    AppSnackBar.showSuccess(context, '注册成功，请登录使用');

                    // 短暂延迟后返回登录页，让用户有时间看到成功提示
                    Future.delayed(const Duration(milliseconds: 1500), () {
                      if (context.mounted) {
                        AppNavigator.goToLogin(context);
                      }
                    });
                  } else if (state is RegisterFailureState) {
                    // 注册失败，显示错误消息
                    AppSnackBar.showError(context, state.message);
                  }
                },
                builder: (context, state) {
                  if (state is RegisterLoadingState) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  return RegisterForm(
                    onRegister: (phone, code, password) {
                      context.read<RegisterBloc>().add(
                            RegisterRequestEvent(
                              phone: phone,
                              code: code,
                              password: password,
                            ),
                          );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
