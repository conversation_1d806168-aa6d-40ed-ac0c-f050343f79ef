/// -----
/// internship_student_list_screen.dart
///
/// 教师端实习生列表页面，展示实习生信息和状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/data/models/internship_student_model.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_student_detail_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class InternshipStudentListScreen extends StatefulWidget {
  const InternshipStudentListScreen({Key? key}) : super(key: key);

  @override
  State<InternshipStudentListScreen> createState() => _InternshipStudentListScreenState();
}

class _InternshipStudentListScreenState extends State<InternshipStudentListScreen> {
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';
  late List<InternshipStudentModel> _students;
  Map<String, bool> _expandedClasses = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    // 获取样例数据
    _students = InternshipStudentModel.getSampleData();

    // 初始化展开状态
    final Set<String> classNames = _students.map((e) => e.className).toSet();
    for (var className in classNames) {
      _expandedClasses[className] = true; // 默认展开
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: '我的实习生',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部
          CourseHeaderSection(
            courseName: _courseName,
            initialExpanded: false,
          ),

          // 学生列表
          Expanded(
            child: _buildStudentGroupList(),
          ),
        ],
      ),
    );
  }

  // 按班级分组的学生列表
  Widget _buildStudentGroupList() {
    // 按班级分组
    final Map<String, List<InternshipStudentModel>> groupedStudents = {};
    for (var student in _students) {
      if (!groupedStudents.containsKey(student.className)) {
        groupedStudents[student.className] = [];
      }
      groupedStudents[student.className]!.add(student);
    }

    return ListView.builder(
      padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
      itemCount: groupedStudents.length,
      itemBuilder: (context, index) {
        final className = groupedStudents.keys.elementAt(index);
        final students = groupedStudents[className]!;

        return _buildClassGroup(className, students);
      },
    );
  }

  // 班级分组
  Widget _buildClassGroup(String className, List<InternshipStudentModel> students) {
    final isExpanded = _expandedClasses[className] ?? true;

    return Column(
      children: [
        // 班级标题栏
        InkWell(
          onTap: () {
            setState(() {
              _expandedClasses[className] = !isExpanded;
            });
          },
          child: Container(
            margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 28.h),
            child: Row(
              children: [
                // 左侧蓝色竖线
                Container(
                  width: 8.w,
                  height: 22.h,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(1.5.r),
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  className,
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.black333,
                  ),
                ),
                const Spacer(),
                Text(
                  isExpanded ? '收起' : '展开',
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: AppTheme.black666,
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  size: 20.sp,
                  color: AppTheme.black666,
                ),
              ],
            ),
          ),
        ),

        // 学生列表
        if (isExpanded)
          ...students.map((student) => _buildStudentItem(student)).toList(),
      ],
    );
  }

  // 学生项
  Widget _buildStudentItem(InternshipStudentModel student) {
    // 根据保险剩余天数确定显示颜色
    final int insuranceDays = int.tryParse(student.insuranceDays) ?? 0;
    final Color insuranceColor = insuranceDays <= 3
        ? Colors.red
        : (insuranceDays <= 7 ? Colors.orange : Colors.black87);

    return InkWell(
      // 添加点击事件，跳转到学生详情页面
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => InternshipStudentDetailScreen(
              studentId: student.id,
            ),
          ),
        );
      },
      // 设置水波纹效果的形状
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        // height: 210.h, // 设置固定高度为210px
        margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 20.h),
        padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 30.h, bottom: 30.h),
        decoration: BoxDecoration(
          color: Colors.white, // 白色背景
          borderRadius: BorderRadius.circular(20.r), // 圆角20px
        ),
      child: Column(
        children: [
          // 上方：姓名、电话和状态标签
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧头像
              CircleAvatar(
                radius: 44.r,
                backgroundImage: NetworkImage(student.avatar),
              ),

              SizedBox(width: 22.w),

              // 姓名
              Text(
                student.name,
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),

              SizedBox(width: 22.w),

              // 电话
              GestureDetector(
                onTap: () async {
                  // 阻止事件冒泡，防止触发整个列表项的点击事件
                  await _handlePhoneCall(student.phone);
                },
                behavior: HitTestBehavior.opaque, // 防止点击事件传递到父级
                child: Row(
                  children: [
                    Icon(
                      Icons.phone,
                      size: 24.sp,
                      color: AppTheme.black666,
                    ),
                    SizedBox(width: 14.w),
                    Text(
                      student.phone,
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: AppTheme.black666,
                      ),
                    ),
                  ],
                ),
              ),

              const Spacer(), // 推动状态标签到最右侧

              // 状态标签
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: const Color(0xFFECF5FF),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  student.status,
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: AppTheme.blue2165f6,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 30.h), // 增加间距

          // 下方：保险信息、安全教育信息和重置密码按钮
          Row(
            crossAxisAlignment: CrossAxisAlignment.center, // 确保垂直居中对齐
            children: [
              // 左侧信息区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 保险信息
                    RichText(
                      text: TextSpan(
                        style: TextStyle(fontSize: 24.sp, color: AppTheme.black999),
                        children: [
                          const TextSpan(text: '保险未覆盖：'),
                          TextSpan(
                            text: '${student.insuranceDays}',
                            style: TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextSpan(
                            text: ' 天',
                            style: TextStyle(
                              color: AppTheme.black666,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 24.h),

                    // 安全教育信息
                    RichText(
                      text: TextSpan(
                        style: TextStyle(fontSize: 24.sp, color: AppTheme.black999),
                        children: [
                          const TextSpan(text: '安全教育试题：'),
                          TextSpan(
                            text: '${student.safetyScore}',
                            style: TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextSpan(
                            text: ' 分',
                            style: TextStyle(
                              color: AppTheme.black666,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 12.w),

              // 右侧重置密码按钮 - 现在与保险和安全教育信息垂直居中对齐
              InkWell(
                onTap: () {
                  // 阻止事件冒泡，防止触发整个列表项的点击事件
                  _showResetPasswordDialog(student);
                },
                // 防止点击事件传递到父级
                excludeFromSemantics: true,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 29.w, vertical: 10.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.black999),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    '重置密码',
                    style: TextStyle(
                      fontSize: 26.sp,
                      color: AppTheme.black666,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      ),
    );
  }

  // 处理电话拨打
  Future<bool> _handlePhoneCall(String phone) async {
    final Uri telLaunchUri = Uri(
      scheme: 'tel',
      path: phone.replaceAll(' ', ''),
    );
    if (await canLaunchUrl(telLaunchUri)) {
      await launchUrl(telLaunchUri);
      return true;
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('无法拨打电话')),
        );
      }
      return false;
    }
  }

  // 重置密码对话框
  void _showResetPasswordDialog(InternshipStudentModel student) {
    // 密码控制器
    final TextEditingController passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Container(
          width: 600.w,
          padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Text(
                '重置学生密码',
                style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
              SizedBox(height: 30.h),

              // 密码输入框
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: TextField(
                  controller: passwordController,
                  decoration: InputDecoration(
                    hintText: '请输入密码(8-16位由数字、大小写字母组成)',
                    hintStyle: TextStyle(
                      fontSize: 28.sp,
                      color: AppTheme.black999,
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
                  ),
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: AppTheme.black333,
                  ),
                  obscureText: true, // 密码隐藏
                ),
              ),
              SizedBox(height: 40.h),

              // 底部按钮
              Row(
                children: [
                  // 取消按钮
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        height: 80.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '取消',
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: AppTheme.black666,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 20.w),
                  // 确定按钮
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        // 验证密码
                        final password = passwordController.text;
                        if (password.isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('请输入密码')),
                          );
                          return;
                        }

                        // 密码格式验证：8-16位数字、大小写字母
                        final RegExp passwordRegex = RegExp(r'^[a-zA-Z0-9]{8,16}$');
                        if (!passwordRegex.hasMatch(password)) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('密码格式不正确，请输入8-16位数字、大小写字母')),
                          );
                          return;
                        }

                        // 关闭对话框
                        Navigator.pop(context);

                        // 显示成功提示
                        AppSnackBar.show(context, '密码重置成功');
                      },
                      child: Container(
                        height: 80.h,
                        decoration: BoxDecoration(
                          color: AppTheme.blue2165f6,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '确定',
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}