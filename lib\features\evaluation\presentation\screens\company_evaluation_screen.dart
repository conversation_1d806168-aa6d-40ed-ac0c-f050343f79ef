/// -----
/// company_evaluation_screen.dart
///
/// 企业评价页面，用于显示邀请码和企业评分流程说明
/// 提供分享功能，方便企业老师下载和使用评分系统
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/immersive_app_bar.dart';
import 'package:flutter_demo/features/evaluation/presentation/widgets/step_item.dart';
import 'package:flutter_demo/features/evaluation/presentation/widgets/step_connector.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 企业评价页面
///
/// 显示学生的邀请码和企业评分流程说明
/// 提供分享功能，帮助企业老师了解评分流程
class CompanyEvaluationScreen extends StatefulWidget {
  /// 学生邀请码
  final String? invitationCode;

  const CompanyEvaluationScreen({
    Key? key,
    this.invitationCode,
  }) : super(key: key);

  @override
  State<CompanyEvaluationScreen> createState() => _CompanyEvaluationScreenState();
}

class _CompanyEvaluationScreenState extends State<CompanyEvaluationScreen> {
  /// 默认邀请码（如果没有传入）
  late String _invitationCode;

  @override
  void initState() {
    super.initState();
    _invitationCode = widget.invitationCode ?? '5056';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const ImmersiveAppBar(
        title: '邀请企业评分',
        titleColor: Colors.white,
        backIconColor: Colors.white,
      ),
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF3E6BFD),
                Color(0xFF4CBBFF),
              ],
            ),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                left: 32.w,
                right: 32.w,
                top: MediaQuery.of(context).padding.top + kToolbarHeight + 20.h,
                bottom: 32.w,
              ),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(24.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20.r,
                      offset: Offset(0, 8.h),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(48.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 邀请码区域
                      _buildInvitationCodeSection(),

                      SizedBox(height: 60.h),

                      // 企业评分流程
                      _buildEvaluationProcessSection(),

                      SizedBox(height: 80.h),

                      // 分享按钮
                      _buildShareButton(),

                      SizedBox(height: 30.h),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建邀请码区域
  Widget _buildInvitationCodeSection() {
    return Center(
      child: Column(
        children: [
          Text(
            '您的邀请码',
            style: TextStyle(
              fontSize: 32.sp,
              color: AppTheme.black666,
            ),
          ),
          SizedBox(height: 24.h),
          GestureDetector(
            onTap: _copyInvitationCode,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 32.w,
                vertical: 16.h,
              ),
              child: Text(
                _invitationCode,
                style: TextStyle(
                  fontSize: 96.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF3E6BFD),
                  letterSpacing: 8.w,
                ),
              ),
            ),
          ),

        ],
      ),
    );
  }

  /// 构建企业评分流程区域
  Widget _buildEvaluationProcessSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '企业评分流程',
          style: TextStyle(
            fontSize: 36.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2165F6),
          ),
        ),

        SizedBox(height: 48.h),

        // 步骤列表
        const StepItem(
          iconPath: 'assets/images/evaluation_step1_icon.png',
          stepNumber: '01',
          description: '企业HR(老师)在应用市场下载AI智能实习管理APP',
        ),

        const StepConnector(height: 40),

        const StepItem(
          iconPath: 'assets/images/evaluation_step2_icon.png',
          stepNumber: '02',
          description: '注册并输入邀请码，填报企业和企业老师个人信息',
        ),

        const StepConnector(height: 40),

        const StepItem(
          iconPath: 'assets/images/evaluation_step3_icon.png',
          stepNumber: '03',
          description: '进入APP首页，点击"实习成绩"，对学生的实习情况进行评分',
        ),
      ],
    );
  }

  /// 构建分享按钮
  Widget _buildShareButton() {
    return SizedBox(
      width: double.infinity,
      height: 88.h,
      child: ElevatedButton(
        onPressed: _shareToCompanyTeacher,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF3E6BFD),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          elevation: 8,
          shadowColor: const Color(0xFF3E6BFD).withOpacity(0.3),
        ),
        child: Text(
          '分享给企业老师',
          style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 复制邀请码到剪贴板
  void _copyInvitationCode() {
    Clipboard.setData(ClipboardData(text: _invitationCode));
    AppSnackBar.showSuccess(context, '邀请码已复制到剪贴板');
  }

  /// 分享给企业老师
  void _shareToCompanyTeacher() {
    // TODO: 实现分享功能
    // 可以使用 share_plus 插件或其他分享方式
    AppSnackBar.show(context, '分享功能开发中...');
  }
}
