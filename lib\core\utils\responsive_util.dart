/// -----------------------------------------------------------------------------
/// responsive_util.dart
///
/// 跨平台响应式适配工具
/// 
/// 功能：
/// 1. 在Web平台使用基于视口的响应式计算
/// 2. 在移动平台使用flutter_screenutil的逻辑
/// 3. 提供与flutter_screenutil相同的API接口
/// 4. 支持Android、iOS和Web平台
///
/// 使用方法：
/// ```dart
/// // 替换 flutter_screenutil 的导入
/// import 'package:flutter_demo/core/utils/responsive_util.dart';
/// 
/// // 使用方式完全相同
/// Container(
///   width: 100.w,
///   height: 50.h,
///   child: Text('Hello', style: TextStyle(fontSize: 16.sp)),
/// )
/// ```
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';


/// 响应式适配工具类
class ResponsiveUtil {
  static ResponsiveUtil? _instance;
  static ResponsiveUtil get instance => _instance ??= ResponsiveUtil._();
  
  ResponsiveUtil._();

  /// 设计稿尺寸
  static const double _designWidth = 750.0;
  static const double _designHeight = 1334.0;
  
  /// 当前屏幕尺寸
  late Size _screenSize;
  late double _scaleWidth;
  late double _scaleHeight;
  late double _scaleText;
  
  /// 是否已初始化
  bool _initialized = false;
  
  /// 初始化响应式适配
  void init(BuildContext context) {
    if (_initialized) return;
    
    if (kIsWeb) {
      // Web平台：使用MediaQuery获取视口尺寸
      _screenSize = MediaQuery.of(context).size;
      _scaleWidth = _screenSize.width / _designWidth;
      _scaleHeight = _screenSize.height / _designHeight;
      _scaleText = _scaleWidth; // Web平台文字缩放使用宽度比例
    } else {
      // 移动平台：使用screenutil的逻辑
      _screenSize = MediaQuery.of(context).size;
      _scaleWidth = _screenSize.width / _designWidth;
      _scaleHeight = _screenSize.height / _designHeight;
      _scaleText = _scaleWidth; // 保持与screenutil一致的文字缩放逻辑
    }
    
    _initialized = true;
  }
  
  /// 获取适配后的宽度
  double getWidth(num width) {
    if (!_initialized) {
      debugPrint('ResponsiveUtil not initialized, using original value');
      return width.toDouble();
    }
    
    if (kIsWeb) {
      // Web平台：限制最小和最大缩放比例，避免过度缩放
      final clampedScale = _scaleWidth.clamp(0.5, 2.0);
      return width * clampedScale;
    } else {
      // 移动平台：使用screenutil的逻辑
      return width * _scaleWidth;
    }
  }
  
  /// 获取适配后的高度
  double getHeight(num height) {
    if (!_initialized) {
      debugPrint('ResponsiveUtil not initialized, using original value');
      return height.toDouble();
    }
    
    if (kIsWeb) {
      // Web平台：限制最小和最大缩放比例
      final clampedScale = _scaleHeight.clamp(0.5, 2.0);
      return height * clampedScale;
    } else {
      // 移动平台：使用screenutil的逻辑
      return height * _scaleHeight;
    }
  }
  
  /// 获取适配后的字体大小
  double getFontSize(num fontSize) {
    if (!_initialized) {
      debugPrint('ResponsiveUtil not initialized, using original value');
      return fontSize.toDouble();
    }
    
    if (kIsWeb) {
      // Web平台：字体缩放更保守，避免过大或过小
      final clampedScale = _scaleText.clamp(0.8, 1.5);
      return fontSize * clampedScale;
    } else {
      // 移动平台：使用screenutil的逻辑
      return fontSize * _scaleText;
    }
  }
  
  /// 获取适配后的圆角半径
  double getRadius(num radius) {
    if (!_initialized) {
      debugPrint('ResponsiveUtil not initialized, using original value');
      return radius.toDouble();
    }
    
    if (kIsWeb) {
      // Web平台：圆角缩放使用较小的比例
      final clampedScale = _scaleWidth.clamp(0.7, 1.3);
      return radius * clampedScale;
    } else {
      // 移动平台：使用screenutil的逻辑
      return radius * _scaleWidth;
    }
  }
  
  /// 获取屏幕宽度
  double get screenWidth => _initialized ? _screenSize.width : 0;
  
  /// 获取屏幕高度
  double get screenHeight => _initialized ? _screenSize.height : 0;
  
  /// 重置初始化状态（用于热重载等场景）
  void reset() {
    _initialized = false;
  }
}

/// 数字扩展方法，提供与flutter_screenutil相同的API
extension ResponsiveExtension on num {
  /// 适配宽度
  double get w => ResponsiveUtil.instance.getWidth(this);
  
  /// 适配高度
  double get h => ResponsiveUtil.instance.getHeight(this);
  
  /// 适配字体大小
  double get sp => ResponsiveUtil.instance.getFontSize(this);
  
  /// 适配圆角半径
  double get r => ResponsiveUtil.instance.getRadius(this);
}

/// 响应式初始化Widget
/// 用于替代ScreenUtilInit
class ResponsiveInit extends StatefulWidget {
  final Size designSize;
  final Widget Function(BuildContext context, Widget? child) builder;
  final Widget? child;
  
  const ResponsiveInit({
    Key? key,
    required this.designSize,
    required this.builder,
    this.child,
  }) : super(key: key);
  
  @override
  State<ResponsiveInit> createState() => _ResponsiveInitState();
}

class _ResponsiveInitState extends State<ResponsiveInit> {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 初始化响应式适配
    ResponsiveUtil.instance.init(context);
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.builder(context, widget.child);
  }
}
