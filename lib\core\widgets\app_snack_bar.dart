import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 应用通用SnackBar工具类
/// 提供统一的SnackBar样式和便捷的显示方法
class AppSnackBar {
  /// 显示普通提示SnackBar
  ///
  /// [context] 上下文
  /// [message] 提示消息
  /// [duration] 显示时长，默认2秒
  static void show(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: 28.sp,
            color: Colors.white,
          ),
        ),
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  /// 显示成功提示SnackBar
  ///
  /// [context] 上下文
  /// [message] 成功消息
  /// [duration] 显示时长，默认2秒
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: duration,
      ),
    );
  }

  /// 显示错误提示SnackBar
  ///
  /// [context] 上下文
  /// [message] 错误消息
  /// [duration] 显示时长，默认3秒
  static void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: duration
      ),
    );
  }

  /// 显示警告提示SnackBar
  ///
  /// [context] 上下文
  /// [message] 警告消息
  /// [duration] 显示时长，默认3秒
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: duration
      ),
    );
  }

  /// 显示带操作按钮的SnackBar
  ///
  /// [context] 上下文
  /// [message] 提示消息
  /// [actionLabel] 操作按钮文本
  /// [onPressed] 操作按钮点击回调
  /// [backgroundColor] 背景颜色，默认为主题色
  /// [duration] 显示时长，默认4秒
  static void showWithAction(
    BuildContext context,
    String message, {
    required String actionLabel,
    required VoidCallback onPressed,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: 28.sp,
            color: Colors.white,
          ),
        ),
        backgroundColor: backgroundColor ?? AppTheme.primaryColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
        action: SnackBarAction(
          label: actionLabel,
          textColor: Colors.white,
          onPressed: onPressed,
        ),
      ),
    );
  }
}
