import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:flutter_demo/core/config/env_config.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/api_debugger.dart';
import 'package:flutter_demo/core/config/injection/injection.dart';
import 'package:flutter_demo/core/router/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置应用只支持竖屏显示
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 根据构建模式选择环境配置
  const environment = kReleaseMode ? EnvType.prod : EnvType.dev;

  // 初始化环境配置
  Env.instance.initialize(environment: environment);

  // 使用环境配置初始化DioClient
  DioClient().initWithConfig(Env.instance.config);

  // 在开发环境下启用API调试
  if (Env.instance.config.isDevelopment) {
    ApiDebugger.instance.enable();
  }

  // 初始化依赖注入
  await setupInjection();

  // 打印当前环境信息
  debugPrint('应用运行在${kReleaseMode ? "Release" : "Debug"}模式');
  debugPrint('使用环境配置: ${Env.instance.config.name}');
  debugPrint('API基础URL: ${Env.instance.config.baseUrl}');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 创建路由器
    final router = AppRouter.createRouter();

    // 打印路由配置信息（仅在调试模式下）
    if (kDebugMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppRouter.printRouteConfiguration(router);
      });
    }

    return ResponsiveInit(
      designSize: const Size(750, 1334),
      builder: (context, child) => MaterialApp.router(
        title: '实习管理系统',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        routerConfig: router,
      ),
    );
  }
}
