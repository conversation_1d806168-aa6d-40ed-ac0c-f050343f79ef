/// -----
/// internship_approval_list_screen.dart
///
/// 实习申请审批列表页面，展示待审批和已审批的实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/internship/data/models/internship_application_model.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_approval_detail_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:flutter_svg/flutter_svg.dart';

class InternshipApprovalListScreen extends StatefulWidget {
  const InternshipApprovalListScreen({Key? key}) : super(key: key);

  @override
  State<InternshipApprovalListScreen> createState() =>
      _InternshipApprovalListScreenState();
}

class _InternshipApprovalListScreenState
    extends State<InternshipApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';

  // 可选的课程列表
  final List<String> _availableCourses = [
    '2023级市场销售2023-2024实习学年岗位实习',
    '2022级市场销售2023-2024实习学年岗位实习',
    '2021级市场销售2023-2024实习学年第二学期岗位实习',
    '2020级市场销售2023-2024实习学年岗位实习',
  ];

  // 分类后的申请列表
  late List<InternshipApplicationModel> _pendingApplications;
  late List<InternshipApplicationModel> _approvedApplications;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    // 获取样例数据
    final List<InternshipApplicationModel> allApplications =
        InternshipApplicationModel.getSampleData();

    // 分类数据
    _pendingApplications =
        allApplications.where((app) => app.status == '待审批').toList();
    _approvedApplications =
        allApplications.where((app) => app.status != '待审批').toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: '实习申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部 - 自动从全局状态获取实习计划数据
          const CourseHeaderSection(),

          // 标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: _pendingApplications.length,
          ),

          // 页面内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPendingList(),
                _buildApprovedList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _pendingApplications.length,
      itemBuilder: (context, index) {
        final item = _pendingApplications[index];
        return _buildApplicationItem(item);
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _approvedApplications.length,
      itemBuilder: (context, index) {
        final item = _approvedApplications[index];
        return _buildApplicationItem(item);
      },
    );
  }

  // 申请项
  Widget _buildApplicationItem(InternshipApplicationModel item) {
    final status = item.status;
    final statusColor = _getStatusColor(status);

    return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InternshipApprovalDetailScreen(
                approvalId: item.id,
                status: item.status,
              ),
            ),
          ).then((result) {
            // 如果返回结果为true，表示审批状态有变化，需要刷新列表
            if (result == true) {
              setState(() {
                _loadData();
              });
            }
          });
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 学生信息行
                Row(
                  children: [
                    // 头像 - 使用SVG作为默认头像
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey[300],
                      ),
                      child: ClipOval(
                        child: item.studentAvatar.isNotEmpty
                            ? Image.network(
                                item.studentAvatar,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    SvgPicture.asset(
                                  'assets/images/default_avatar.svg',
                                  fit: BoxFit.cover,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/images/default_avatar.svg',
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 学生姓名
                    Text(
                      item.studentName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    // 状态标签
                    Text(
                      status,
                      style: TextStyle(
                        fontSize: 14,
                        color: statusColor,
                      ),
                    ),
                  ],
                ),

                // 添加分割线
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: Divider(
                      height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
                ),

                // 实习单位
                _buildInfoRow('实习单位', item.companyName),

                const SizedBox(height: 8),

                // 部门/科室
                _buildInfoRow('部门/科室', item.department),

                const SizedBox(height: 8),

                // 实习岗位
                _buildInfoRow('实习岗位', item.position),

                const SizedBox(height: 16),

                // 申请时间和查看按钮
                Row(
                  children: [
                    Text(
                      '申请时间：${item.applyDate}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    // 查看按钮
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                InternshipApprovalDetailScreen(
                              approvalId: item.id,
                              status: item.status,
                            ),
                          ),
                        ).then((result) {
                          // 如果返回结果为true，表示审批状态有变化，需要刷新列表
                          if (result == true) {
                            setState(() {
                              _loadData();
                            });
                          }
                        });
                      },
                      child: const Row(
                        children: [
                          Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                            color: AppTheme.primaryColor,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
    );
  }

  // 信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 根据状态获取颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case '待审批':
        return Colors.grey;
      case '已通过':
        return Colors.green;
      case '已驳回':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
