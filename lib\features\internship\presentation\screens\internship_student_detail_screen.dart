/// -----
/// internship_student_detail_screen.dart
///
/// 实习生详情页面，展示实习生的详细信息
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/data/models/internship_student_detail_model.dart';
import 'package:flutter_demo/features/internship/presentation/screens/attachment_detail_screen.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_item.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/info_section_card.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/sign_progress_ring.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class InternshipStudentDetailScreen extends StatefulWidget {
  final String studentId;

  const InternshipStudentDetailScreen({
    Key? key,
    required this.studentId,
  }) : super(key: key);

  @override
  State<InternshipStudentDetailScreen> createState() =>
      _InternshipStudentDetailScreenState();
}

class _InternshipStudentDetailScreenState
    extends State<InternshipStudentDetailScreen>
    with SingleTickerProviderStateMixin {
  late InternshipStudentDetailModel _student;
  late TabController _tabController;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期\n岗位实习';

  // 维护每个企业项目的展开/折叠状态
  List<bool> _companyExpandedStates = [];

  @override
  void initState() {
    super.initState();
    // TODO: 根据studentId获取实习生详情数据
    _student = InternshipStudentDetailModel.getSampleData();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      if (mounted) setState(() {});
    });

    // 初始化企业展开状态，默认都是折叠状态
    _companyExpandedStates = List.generate(_student.companies.length, (index) => false);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '实习生详情',
        centerTitle: true,
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCourseHeader(),
            SizedBox(height: 20.h,),
            _buildBasicInfo(),
            _buildInsuranceInfo(),
            _buildSafetyExamInfo(),
            _buildAttachments(),
            _buildTabBar(),
            // 只渲染当前Tab内容
            Builder(
              builder: (context) {
                switch (_tabController.index) {
                  case 0:
                    return _buildSignAndReportCard();
                  case 1:
                    return _buildPositionTab();
                  case 2:
                    return _buildScoreTab();
                  default:
                    return Container();
                }
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseHeader() {
    return CourseHeaderSection(courseName: _courseName);
  }

  // 基本信息
  Widget _buildBasicInfo() {
    return InfoSectionCard(
      title: '基本信息',
      icon: CircleAvatar(
        radius: 22.r,
        backgroundImage: NetworkImage(AppConstants.avatar1),
      ),
      child: Column(
        children: [
          InfoItem(
            label: '班级',
            value: _student.className,
          ),
          InfoItem(
            label: '电话',
            value: _student.phone,
            trailing: GestureDetector(
              onTap: () => _makePhoneCall(_student.phone),
              child: const Icon(
                Icons.phone,
                size: 16,
                color: Colors.blue,
              ),
            ),
          ),
          InfoItem(
            label: '企业指导老师',
            value: _student.companyTeacher,
            trailing: GestureDetector(
              onTap: () => _makePhoneCall(_student.companyTeacherPhone),
              child: const Icon(
                Icons.phone,
                size: 16,
                color: Colors.blue,
              ),
            ),
          ),
          InfoItem(
            label: '家长联系人',
            value: _student.emergencyContact,
            trailing: GestureDetector(
              onTap: () => _makePhoneCall(_student.emergencyPhone),
              child: const Icon(
                Icons.phone,
                size: 16,
                color: Colors.blue,
              ),
            ),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  // 实习保险信息
  Widget _buildInsuranceInfo() {
    final insuranceInfo = _student.insuranceInfo;

    return InfoSectionCard(
      title: '实习保险信息',
      icon: Image.asset(
          'assets/images/job_information_icon.png', width: 25.w, height: 28.h),
      child: Column(
        children: [
          InfoItem(
            label: '保险名称',
            value: insuranceInfo.name,
          ),
          InfoItem(
            label: '保险单号',
            value: insuranceInfo.policyNumber,
          ),
          InfoItem(
            label: '保险购买方',
            value: insuranceInfo.purchaser,
          ),
          InfoItem(
            label: '保险种类',
            value: insuranceInfo.type,
          ),
          InfoItem(
            label: '保险起止时间',
            value: '${insuranceInfo.startDate} 至 ${insuranceInfo.endDate}',
            showDivider: false,
          ),
        ],
      ),
    );
  }

  // 安全教育考试信息
  Widget _buildSafetyExamInfo() {
    return InfoSectionCard(
      title: '安全教育考试',
      icon: Image.asset(
          'assets/images/internship_edu_exam_icon.png', width: 25.w,
          height: 28.h),
      child: Container(
        margin: EdgeInsets.only(top: 39.h),
        padding: const EdgeInsets.only(left: 16),
        child: Row(
          children: [
            const Text('考试得分：',
                style: TextStyle(
                    color: AppTheme.black999,
                    fontSize: 14)),
            SizedBox(width: 10.w),
            const Text('80分',
                style: TextStyle(
                    color: AppTheme.black333,
                    fontSize: 14
                )),
            const Spacer(),
            GestureDetector(
              onTap: () {
                // TODO: 实现安全教育考试详情页面导航
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('安全教育考试详情功能开发中')),
                );
              },
              child: Row(
                children: [
                  Text(
                    '查看详情',
                    style: TextStyle(
                      color: const Color(0xFF2165F6),
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 12,
                    color: const Color(0xFF2165F6),
                  ),
                  SizedBox(width: 16.w),
                ],
              ),
            ),
          ],
        ),
      )
    );
  }

  // 附件信息
  Widget _buildAttachments() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 0.h),
      padding: EdgeInsets.symmetric(vertical: 30.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: _student.attachments.asMap().entries.map((entry) {
          final index = entry.key;
          final attachment = entry.value;

          return Container(
            margin: EdgeInsets.only(
              left: 30.w,
              right: 30.w,
              bottom: index == _student.attachments.length - 1 ? 0 : 20.h,
            ),
            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.h),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F8F8),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                // 文档图标 - 使用蓝色W图标
                Image.asset('assets/images/upload_blue_doc_icon.png', width: 40.w, height: 48.h),
                SizedBox(width: 16.w),

                // 文件名
                Expanded(
                  child: Text(
                    attachment.name,
                    style: TextStyle(
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.black333,
                    ),
                  ),
                ),

                // 更多按钮
                GestureDetector(
                  onTap: () {
                    // 显示操作菜单
                    _showAttachmentOptions(attachment);
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: Icon(
                      Icons.more_horiz,
                      color: Colors.grey[400],
                      size: 32.sp,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  // 底部标签栏
  Widget _buildTabBar() {
    return Container(
      margin: EdgeInsets.only(left: 25.w,right: 25.w,top: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            labelColor: AppTheme.blue2165f6,
            unselectedLabelColor: AppTheme.black666,
            indicatorColor: AppTheme.blue2165f6,
            indicatorSize: TabBarIndicatorSize.label,
            indicatorWeight: 3.h,
            labelStyle: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 28.sp,
            ),
            tabs: const [
              Tab(text: '实习过程'),
              Tab(text: '实习岗位'),
              Tab(text: '实习成绩'),
            ],
          ),
          Divider(height: 1, thickness: 0.5, color: Colors.grey[200]),
        ],
      ),
    );
  }

  // 签到情况+实习汇报整体卡片
  Widget _buildSignAndReportCard() {
    final sign = _student.signInfo;
    final reports = _student.reports;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      padding: EdgeInsets.symmetric(vertical: 30.h, horizontal: 30.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 签到情况标题行
          Row(
            children: [
              Text(
                '签到情况',
                style: TextStyle(
                  color: AppTheme.black333,
                  fontSize: 32.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  // TODO: 跳转到签到详情页面
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '查看详情',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 28.sp,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: AppTheme.primaryColor,
                      size: 24.sp,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 30.h),
          // 签到情况内容区域
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 圆环进度
              SignProgressRing(
                signedDays: sign.signedDays,
                totalDays: sign.totalDays,
                size: 200,
                ringColor: const Color(0xFF2165F6),
                label: '签到天数',
                strokeWidth: 12,
              ),
              SizedBox(width: 40.w),
              // 右侧数据
              Expanded(
                child: Column(
                  children: [
                    // 补签天数
                    _buildSignDataItem('补签天数', sign.supplementDays),
                    SizedBox(height: 40.h),
                    // 免签天数
                    _buildSignDataItem('免签天数', sign.exemptDays),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 40.h),
          // 分割线
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEDEDED)),
          SizedBox(height: 30.h),
          // 实习汇报标题
          Text(
            '实习汇报',
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 30.h),
          // 实习汇报列表
          ...reports.asMap().entries.map((entry) {
            final index = entry.key;
            final report = entry.value;
            final double progress = report.total == 0 ? 0 : report.completed / report.total;

            // 根据设计图确定进度条颜色
            Color progressColor;
            switch (report.type) {
              case '日报':
                progressColor = const Color(0xFF4B7CFF);
                break;
              case '月报':
                progressColor = const Color(0xFF8A4BFF);
                break;
              case '周报':
                progressColor = const Color(0xFF00C3B6);
                break;
              case '实习总结':
                progressColor = const Color(0xFFFFA726);
                break;
              default:
                progressColor = const Color(0xFF4B7CFF);
            }

            return Container(
              margin: EdgeInsets.only(bottom: index == reports.length - 1 ? 0 : 30.h),
              child: Row(
                children: [
                  // 报告类型标签
                  SizedBox(
                    width: 80.w,
                    child: Text(
                      report.type,
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: AppTheme.black333,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  SizedBox(width: 20.w),
                  // 进度条
                  Expanded(
                    child: Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        Container(
                          height: 12.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFFEDEDED),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                        ),
                        FractionallySizedBox(
                          widthFactor: progress,
                          child: Container(
                            height: 12.h,
                            decoration: BoxDecoration(
                              color: progressColor,
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 20.w),
                  // 完成数量
                  SizedBox(
                    width: 80.w,
                    child: Text(
                      '${report.completed}/${report.total}',
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: AppTheme.black333,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  // 构建签到数据项
  Widget _buildSignDataItem(String label, int value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF999999),
          ),
        ),
        SizedBox(height: 12.h),
        Text(
          '$value',
          style: TextStyle(
            fontSize: 48.sp,
            color: AppTheme.black333,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  // 实习成绩Tab内容
  Widget _buildScoreTab() {
    // 示例数据，实际可从_student.safetyExamInfo等获取
    final totalScore = 80;
    final scoreList = [
      {'subject': '校内老师评分', 'ratio': '50%', 'score': '待评'},
      {'subject': '企业综合评分', 'ratio': '50%', 'score': '100'},
    ];

    return Container(
      margin: EdgeInsets.only(left: 25.w,right: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 总成绩
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 20, bottom: 12),
            child: Row(
              children: [
                const Text(
                  '考核总成绩：',
                  style: TextStyle(fontSize: 16, color: Color(0xFF222222)),
                ),
                Text(
                  '$totalScore分',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2165F6),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // 表头
          Container(
            margin: EdgeInsets.symmetric(vertical: 12, horizontal: 30.w),
            padding: EdgeInsets.symmetric(horizontal: 19.w),
            height: 80.h,
            decoration: const BoxDecoration(
              color: Color(0xFFF7F8FA),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    '考核科目',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    '占分比例',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    '分数',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEDEDED)),
          // 内容
          ...scoreList.asMap().entries.map((entry) {
            final idx = entry.key;
            final item = entry.value;
            return Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          item['subject'] ?? '',
                          style: const TextStyle(fontSize: 14, color: Color(0xFF222222)),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          item['ratio'] ?? '',
                          style: const TextStyle(fontSize: 14, color: Color(0xFF222222)),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          item['score'] ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: item['score'] == '待评'
                                ? AppTheme.black999
                                : const Color(0xFF2165F6),
                            fontWeight: item['score'] == '待评'
                                ? FontWeight.normal
                                : FontWeight.bold,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
                if (idx != scoreList.length - 1)
                  const Divider(height: 1, thickness: 0.5, color: Color(0xFFEDEDED)),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  // 实习岗位Tab内容
  Widget _buildPositionTab() {
    return Container(
      margin: EdgeInsets.only(left: 25.w, right: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: _student.companies.asMap().entries.map((entry) {
          final index = entry.key;
          final company = entry.value;
          final isExpanded = _companyExpandedStates[index];

          return _buildCompanyItem(company, index, isExpanded);
        }).toList(),
      ),
    );
  }

  // 构建单个企业信息项
  Widget _buildCompanyItem(dynamic company, int index, bool isExpanded) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 基本信息行（企业名称 + 企业统一信用代码 + 展开/收起按钮）
          _buildBasicInfoRow(company, index, isExpanded),

          // 展开的详细信息
          if (isExpanded) ...[
            const SizedBox(height: 16),
            _buildDetailedInfo(company),
          ],

          // 分割线（最后一项不显示）
          if (index != _student.companies.length - 1)
            Container(
              margin: const EdgeInsets.only(top: 16),
              child: const Divider(height: 1, thickness: 0.5, color: Color(0xFFEDEDED)),
            ),
        ],
      ),
    );
  }

  // 构建基本信息行
  Widget _buildBasicInfoRow(dynamic company, int index, bool isExpanded) {
    return Column(
      children: [
        InfoItem(label: '企业名称', value: company.companyName),
        const SizedBox(height: 12),
        InfoItem(label: '企业统一信用代码', value: company.companyCreditCode),
        const SizedBox(height: 12),
        // 展开/收起按钮单独一行，居右对齐
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            GestureDetector(
              onTap: () {
                setState(() {
                  _companyExpandedStates[index] = !_companyExpandedStates[index];
                });
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    isExpanded ? '收起' : '展开',
                    style: const TextStyle(
                      color: Color(0xFF999999),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: const Color(0xFF999999),
                    size: 16,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 构建详细信息
  Widget _buildDetailedInfo(dynamic company) {
    return Column(
      children: [
        InfoItem(label:'企业规模', value:company.companyScale),
        const SizedBox(height: 12),
        InfoItem(label: '所属区域',value:company.companyRegion),
        const SizedBox(height: 12),
        InfoItem(label: '详细地址',value: company.companyAddress),
        const SizedBox(height: 12),
        InfoItem(label: '企业性质', value:company.companyNature),
        const SizedBox(height: 12),
        InfoItem(label: '所属行业',value:  company.companyIndustry),
        const SizedBox(height: 12),
        InfoItem(label: '企业联系人',value:  company.companyContact),
        const SizedBox(height: 12),
        InfoItem(label: '企业联系人电话',value:  company.companyContactPhone),
      ],
    );
  }


  // 拨打电话
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri telLaunchUri = Uri(
      scheme: 'tel',
      path: phoneNumber.replaceAll(' ', ''),
    );
    if (await canLaunchUrl(telLaunchUri)) {
      await launchUrl(telLaunchUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('无法拨打电话')),
        );
      }
    }
  }

  // 显示附件操作选项
  void _showAttachmentOptions(AttachmentInfo attachment) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 20.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.visibility, color: Colors.blue),
                title: const Text('查看'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          AttachmentDetailScreen(
                            studentId: widget.studentId,
                            attachmentName: attachment.name,
                            attachmentUrl: attachment.url,
                          ),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.download, color: Colors.green),
                title: const Text('下载'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('开始下载文件...')),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.share, color: Colors.orange),
                title: const Text('分享'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('分享功能暂未实现')),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}