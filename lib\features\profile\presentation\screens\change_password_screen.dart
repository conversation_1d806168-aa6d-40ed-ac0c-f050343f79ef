import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 修改密码页面
///
/// 允许用户修改其账户密码
/// 包含旧密码验证、新密码输入和确认新密码三个步骤
class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({Key? key}) : super(key: key);

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  // 表单控制器
  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // 表单验证状态
  bool _isOldPasswordValid = true;
  bool _isNewPasswordValid = true;
  bool _isConfirmPasswordValid = true;

  // 密码可见性状态
  bool _isOldPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void dispose() {
    // 释放控制器资源
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 验证并提交表单
  void _submitForm() {
    // 验证旧密码
    if (_oldPasswordController.text.isEmpty) {
      setState(() {
        _isOldPasswordValid = false;
      });
      return;
    }

    // 验证新密码 (6-20位数字或字母)
    final newPassword = _newPasswordController.text;
    if (newPassword.isEmpty || newPassword.length < 6 || newPassword.length > 20 || !RegExp(r'^[a-zA-Z0-9]+$').hasMatch(newPassword)) {
      setState(() {
        _isNewPasswordValid = false;
      });
      return;
    }

    // 验证确认密码
    if (_confirmPasswordController.text != newPassword) {
      setState(() {
        _isConfirmPasswordValid = false;
      });
      return;
    }

    // 所有验证通过，执行密码修改
    // TODO: 实现实际的密码修改逻辑，可能需要调用API

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('密码修改成功')),
    );

    // 返回上一页
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: CustomAppBar(
        title: '修改密码',
        actions: [
          TextButton(
            onPressed: _submitForm,
            child: Text(
              '完成',
              style: TextStyle(
                color: AppTheme.blue2165f6,
                fontSize: 28.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部分隔线
            Divider(height: 1.h, thickness: 1, color: Colors.grey[300]),

            // 密码输入区域
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  // 旧密码输入框
                  _buildPasswordField(
                    label: '旧密码',
                    hintText: '请输入旧密码',
                    controller: _oldPasswordController,
                    isValid: _isOldPasswordValid,
                    errorText: '请输入旧密码',
                    isPasswordVisible: _isOldPasswordVisible,
                    onTogglePasswordVisibility: () {
                      setState(() {
                        _isOldPasswordVisible = !_isOldPasswordVisible;
                      });
                    },
                    onChanged: (value) {
                      if (!_isOldPasswordValid) {
                        setState(() {
                          _isOldPasswordValid = true;
                        });
                      }
                    },
                  ),

                  // 新密码输入框
                  _buildPasswordField(
                    label: '新密码',
                    hintText: '请输入新密码 (6-20位数字或字母)',
                    controller: _newPasswordController,
                    isValid: _isNewPasswordValid,
                    errorText: '请输入6-20位数字或字母的密码',
                    isPasswordVisible: _isNewPasswordVisible,
                    onTogglePasswordVisibility: () {
                      setState(() {
                        _isNewPasswordVisible = !_isNewPasswordVisible;
                      });
                    },
                    onChanged: (value) {
                      if (!_isNewPasswordValid) {
                        setState(() {
                          _isNewPasswordValid = true;
                        });
                      }
                    },
                  ),

                  // 确认密码输入框
                  _buildPasswordField(
                    label: '确认密码',
                    hintText: '请再次输入新密码',
                    controller: _confirmPasswordController,
                    isValid: _isConfirmPasswordValid,
                    errorText: '两次输入的密码不一致',
                    isPasswordVisible: _isConfirmPasswordVisible,
                    onTogglePasswordVisibility: () {
                      setState(() {
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                      });
                    },
                    onChanged: (value) {
                      if (!_isConfirmPasswordValid) {
                        setState(() {
                          _isConfirmPasswordValid = true;
                        });
                      }
                    },
                    isLast: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建密码输入字段
  Widget _buildPasswordField({
    required String label,
    required String hintText,
    required TextEditingController controller,
    required bool isValid,
    required String errorText,
    required bool isPasswordVisible,
    required VoidCallback onTogglePasswordVisibility,
    required Function(String) onChanged,
    bool isLast = false,
  }) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 标签和输入框在一行
          Row(
            crossAxisAlignment: CrossAxisAlignment.center, // 确保水平居中对齐
            children: [
              // 标签
              Container(
                height: 100.h, // 与输入框相同高度
                alignment: Alignment.center, // 垂直居中
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 32.sp,
                    color: AppTheme.black333,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              SizedBox(width: 20.w),
              // 输入框
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end, // 错误信息右对齐
                  children: [
                    Container(
                      height: 55.h,
                      color: Colors.white, // 纯白色背景
                      alignment: Alignment.center, // 确保内容垂直居中
                      child: TextField(
                        controller: controller,
                        obscureText: !isPasswordVisible,
                        onChanged: onChanged,
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: AppTheme.black333,
                        ),
                        decoration: InputDecoration(
                          hintText: hintText,
                          hintStyle: TextStyle(
                            color: AppTheme.black999,
                            fontSize: 28.sp,
                          ),
                          // 调整内边距确保文本垂直居中
                          contentPadding: EdgeInsets.symmetric(vertical: 10.h),
                          filled: true,
                          fillColor: Colors.white, // 纯白色背景
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          focusedErrorBorder: InputBorder.none,
                          // 移除内置错误文本，我们将自己显示
                          errorText: null,
                          suffixIcon: isPasswordVisible ? IconButton(
                            icon: Icon(
                              Icons.visibility_off,
                              color: Colors.grey[400],
                              size: 40.sp,
                            ),
                            onPressed: onTogglePasswordVisibility,
                            padding: EdgeInsets.zero,
                          ) : IconButton(
                            icon: Icon(
                              Icons.visibility,
                              color: Colors.grey[400],
                              size: 40.sp,
                            ),
                            onPressed: onTogglePasswordVisibility,
                            padding: EdgeInsets.zero,
                          ),
                          isDense: true,
                        ),
                      ),
                    ),
                    // 自定义错误提示，右对齐
                    if (!isValid)
                      Padding(
                        padding: EdgeInsets.only(top: 4.h, right: 8.w),
                        child: Text(
                          errorText,
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 24.sp,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          // 分隔线
          if (!isLast)
            Divider(height: 1.h, thickness: 0.5, color: Colors.grey[200]),
        ],
      ),
    );
  }
}
