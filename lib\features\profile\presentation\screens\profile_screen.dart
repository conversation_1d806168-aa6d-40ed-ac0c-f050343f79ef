/// 个人资料页面模块
///
/// 该模块展示用户的个人资料信息和相关功能菜单，包括：
/// - 个人基本信息展示
/// - 身份认证入口
/// - 实习信息查看
/// - 企业评价功能
/// - 企业老师信息管理
/// - 实习投诉通道
/// - 帮助与反馈
/// - 服务协议
/// - 应用设置
///
/// 作为应用的主要导航页面之一，提供用户访问个人相关功能的入口。

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/auth/data/models/user_model.dart';
import 'package:flutter_demo/features/profile/presentation/screens/personal_info_screen.dart';
import 'package:flutter_demo/features/settings/presentation/screens/feedback_screen.dart';
import 'package:flutter_demo/features/settings/presentation/screens/settings_screen.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 个人资料页面
///
/// 展示用户个人信息和提供各种功能入口的页面组件
/// 包含用户头像、姓名、身份标签和电话号码等基本信息
/// 以及各种功能菜单项，如身份认证、实习信息、企业评价等
/// 根据用户类型（学生/教师/企业HR）显示不同的布局
class ProfileScreen extends StatefulWidget {
  /// 构造函数
  ///
  /// @param key Widget的键，用于在widget树中唯一标识此widget
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  /// 用户类型
  /// 1: 学生, 2: 教师, 3: 企业HR
  String _userType = '1'; // 默认为教师类型

  /// 用户名称
  String _userName = '张若昀';

  /// 用户手机号
  String _userPhone = '185*****563';

  /// 是否正在加载
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  /// 加载用户信息
  Future<void> _loadUserInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userType = prefs.getString(AppConstants.userTypeKey);
      final userJson = prefs.getString(AppConstants.userKey);
      final userName = UserModel.fromJson(json.decode(userJson!)).userName ?? '未登录';
      final userPhone = UserModel.fromJson(json.decode(userJson!)).phone ?? '***********';

      setState(() {
        if (userType != null) {
          _userType = userType;
        }
        _userName = userName;
        _userPhone = userPhone;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载用户信息失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 构建Widget树
  ///
  /// @param context 构建上下文
  /// @return 返回构建的Widget
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      body: Stack(
        children: [
          // 背景图片
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              width: double.infinity,
              height: 896.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/my_top_bg.png'),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // 内容区域
          SingleChildScrollView(
            child: Column(
              children: [
                // 顶部个人信息区域
                GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PersonalInfoScreen(),
                      ),
                    );
                  },
                  child: _buildProfileHeader(),
                ),

                // 学生端特有的联系人信息区域
                if (_userType == 1) _buildStudentContactInfo(),

                // 菜单项列表
                _buildMenuItems(context),

                // 底部空间，避免内容被底部导航栏遮挡
                SizedBox(height: 80.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建顶部个人信息区域
  ///
  /// 包含用户头像、姓名、身份标签和电话号码
  /// 点击此区域可导航到个人信息详情页面
  ///
  /// @return 返回包含用户个人信息的Widget
  Widget _buildProfileHeader() {
    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;
    // 顶部区域总高度 - 只包含用户信息部分
    final headerHeight = 300.h;

    return Container(
      width: double.infinity,
      height: headerHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头像 - 添加白色边框和固定位置
          Container(
            margin: EdgeInsets.only(
              left: 32.w,
              top: 116.h, // 116px从顶部算起(包括状态栏)
            ),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 6.w,
              ),
            ),
            child: CircleAvatar(
              radius: 70.r,
              backgroundColor: Colors.white,
              backgroundImage: const NetworkImage(AppConstants.avatar1),
            ),
          ),
          SizedBox(width: 28.w),

          // 用户信息
          Container(
            margin: EdgeInsets.only(
              top: 130.h
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Text(
                      _userName,
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 36.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 3.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppTheme.blue2165f6,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        _userType == '1' ? '学生' : (_userType == '2' ? '班主任' : '企业HR'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20.sp,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 30.h),
                Text(
                  _userPhone,
                  style: TextStyle(
                    color: AppTheme.black666,
                    fontSize: 24.sp,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          // 箭头 - 放在右侧，距离右边缘30px
          Container(
            alignment: Alignment.centerRight,
            margin: EdgeInsets.only(
              right: 40.w,
              top: 110.h, // 垂直居中位置
            ),
            child: Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.black666,
              size: 30.sp,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建学生端特有的联系人信息区域
  ///
  /// 显示班主任、学校指导老师和企业指导老师的联系信息
  ///
  /// @return 返回包含联系人信息的Widget
  Widget _buildStudentContactInfo() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        left: 26.w,
        right: 26.w,
        top: 0,
        bottom: 10.h
      ),
      padding: EdgeInsets.symmetric(horizontal: 30.w,vertical:40.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          _buildContactItem(
            title: '班主任',
            phone: '185*****563',
            icon: Icons.visibility,
          ),
          SizedBox(height: 29.h),
          _buildContactItem(
            title: '学校指导老师',
            phone: '18548635563',
            icon: Icons.visibility,
          ),
          SizedBox(height: 29.h),
          _buildContactItem(
            title: '企业指导老师',
            phone: '18597451156',
            icon: Icons.visibility,
          ),
        ],
      ),
    );
  }

  /// 构建联系人项
  ///
  /// @param title 联系人标题
  /// @param phone 联系人电话
  /// @param icon 操作图标
  /// @return 返回包含联系人信息的Widget
  Widget _buildContactItem({
    required String title,
    required String phone,
    required IconData icon,
  }) {
    return Row(
      children: [
        Text(
          '$title: ',
          style: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black666,
          ),
        ),
        Text(
          phone,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black666,
          ),
        ),
        SizedBox(width: 30.w),
        Icon(
          icon,
          size: 18,
          color: Colors.grey,
        ),
      ],
    );
  }

  /// 构建菜单项列表
  ///
  /// 创建并返回包含各种功能入口的菜单列表
  /// 每个菜单项包含图标、标题和点击事件处理
  ///
  /// @param context 构建上下文，用于导航到其他页面
  /// @return 返回包含所有菜单项的ListView
  Widget _buildMenuItems(BuildContext context) {
    // 定义所有菜单项的配置信息
    // 每个菜单项包含：图标、标题、图标颜色、额外组件和点击事件处理
    final List<Map<String, dynamic>> menuItems = [
      {
        'icon': Icons.verified_user_outlined,
        'title': '身份认证',
        'iconColor': AppTheme.blue2165f6,
        'trailingWidget': null,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PersonalInfoScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.support_agent_outlined,
        'title': '帮助与反馈',
        'iconColor': AppTheme.blue2165f6,
        'trailingWidget': null,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const FeedbackScreen(),
            ),
          );
        },
      },
      {
        'icon': Icons.article_outlined,
        'title': '服务协议',
        'iconColor': AppTheme.blue2165f6,
        'trailingWidget': null,
        'onTap': () {},
      },
      {
        'icon': Icons.settings_outlined,
        'title': '设置',
        'iconColor': AppTheme.blue2165f6,
        'trailingWidget': null,
        'onTap': () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SettingsScreen(),
            ),
          );
        },
      },
    ];

    // 根据用户类型添加特定菜单项
    List<Map<String, dynamic>> finalMenuItems = [...menuItems];

    // 返回自适应高度的容器，而不是占满剩余空间
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      // 使用Column和ListView组合，使列表高度自适应内容
      child: Column(
        mainAxisSize: MainAxisSize.min, // 关键设置：使Column高度自适应内容
        children: [
          for (int index = 0; index < finalMenuItems.length; index++) ...[
            _buildMenuItem(finalMenuItems[index]),
          ],
        ],
      ),
    );
  }

  /// 构建单个菜单项
  ///
  /// @param item 菜单项配置信息
  /// @return 返回构建的菜单项Widget
  Widget _buildMenuItem(Map<String, dynamic> item) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
      leading: Image.asset(
        'assets/images/${_getIconAssetName(item['title'] as String)}',
        width: 35.w,
        height: 35.h
      ),
      title: Text(
        item['title'] as String,
        style: TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.w500,
          color: AppTheme.black333,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          item['trailingWidget'] != null
              ? item['trailingWidget'] as Widget
              : const SizedBox.shrink(),
          SizedBox(width: 10.w),
          Icon(
            Icons.arrow_forward_ios,
            color: AppTheme.black999,
            size: 30.sp,
          ),
        ],
      ),
      onTap: item['onTap'] as VoidCallback,
    );
  }

  /// 根据菜单项标题获取对应的图标资源名称
  ///
  /// @param title 菜单项标题
  /// @return 返回图标资源名称
  String _getIconAssetName(String title) {
    switch (title) {
      case '身份认证':
        return 'my_identity_authentication_icon.png';
      case '帮助与反馈':
        return 'my_help_feedback_icon.png';
      case '服务协议':
        return 'my_service_agreement_icon.png';
      case '设置':
        return 'my_setting_icon.png';
      case '实习信息一览':
        return 'internship_information_icon.png';
      case '企业评价':
        return 'home_company_review_icon.png';
      case '企业老师信息':
        return 'company_icon.png';
      case '实习投诉':
        return 'home_complaint_icon.png';
      default:
        return 'icon_default.png';
    }
  }
}