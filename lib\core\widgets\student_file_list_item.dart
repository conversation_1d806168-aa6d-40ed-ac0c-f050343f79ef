/// -----
/// student_file_list_item.dart
///
/// 学生文件列表项组件，用于显示学生信息及其上传的文件列表
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 学生文件信息模型
class StudentFileInfo {
  final String studentId;
  final String studentName;
  final String studentAvatar;
  final String status; // 待审批、已审批
  final List<FileInfo> files;

  const StudentFileInfo({
    required this.studentId,
    required this.studentName,
    required this.studentAvatar,
    required this.status,
    required this.files,
  });
}

/// 文件信息模型
class FileInfo {
  final String fileId;
  final String fileName;
  final String iconPath;
  final VoidCallback? onTap;

  const FileInfo({
    required this.fileId,
    required this.fileName,
    required this.iconPath,
    this.onTap,
  });
}

/// 学生文件列表项组件
/// 
/// 显示学生基本信息和该学生上传的所有文件
class StudentFileListItem extends StatelessWidget {
  /// 学生文件信息
  final StudentFileInfo studentFileInfo;
  
  /// 是否为待审批状态
  final bool isPending;

  const StudentFileListItem({
    Key? key,
    required this.studentFileInfo,
    this.isPending = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 10.h),
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生信息行
          _buildStudentHeader(),
          
          SizedBox(height: 24.h),
          
          // 文件列表
          _buildFileList(),
        ],
      ),
    );
  }

  /// 构建学生信息头部
  Widget _buildStudentHeader() {
    return Row(
      children: [
        // 学生头像
        CircleAvatar(
          radius: 40.r,
          backgroundImage: NetworkImage(studentFileInfo.studentAvatar),
          backgroundColor: const Color(0xFF2165F6).withValues(alpha: 0.1),
        ),
        
        SizedBox(width: 24.w),
        
        // 学生姓名
        Expanded(
          child: Text(
            studentFileInfo.studentName,
            style: TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF222222),
            ),
          ),
        ),
        
        // 状态标签
        _buildStatusTag(),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusTag() {
    final isWaiting = studentFileInfo.status == '待审批';
    return Text(
        studentFileInfo.status,
        style: TextStyle(
          fontSize: 24.sp,
          color: isWaiting ? AppTheme.primaryColor : const Color(0xFF00C853),
          fontWeight: FontWeight.w500,
        ),
      );
  }

  /// 构建文件列表
  Widget _buildFileList() {
    return Column(
      children: studentFileInfo.files.map((file) => _buildFileItem(file)).toList(),
    );
  }

  /// 构建单个文件项
  Widget _buildFileItem(FileInfo file) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: InkWell(
        onTap: file.onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            children: [
              // 文件图标
              Image.asset(
                file.iconPath,
                width: 40.w,
                height: 48.h,
              ),
              
              SizedBox(width: 16.w),
              
              // 文件名
              Expanded(
                child: Text(
                  file.fileName,
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: const Color(0xFF222222),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              // 箭头图标
              Icon(
                Icons.chevron_right,
                size: 40.w,
                color: const Color(0xFFCCCCCC),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
