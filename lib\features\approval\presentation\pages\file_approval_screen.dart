/// -----
/// file_approval_screen.dart
///
/// 教师端文件审批页面，显示需要审批的文件列表及审批进度统计
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/file_list_item.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/file_approval_list/file_approval_list_bloc.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/file_approval_list/file_approval_list_event.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/file_approval_list/file_approval_list_state.dart';
import 'package:flutter_demo/features/approval/core/utils/file_type_mapper.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

class FileApprovalScreen extends StatefulWidget {
  const FileApprovalScreen({Key? key}) : super(key: key);

  @override
  State<FileApprovalScreen> createState() => _FileApprovalScreenState();
}

class _FileApprovalScreenState extends State<FileApprovalScreen> {
  late FileApprovalListBloc _fileApprovalListBloc;

  @override
  void initState() {
    super.initState();
    _fileApprovalListBloc = GetIt.instance<FileApprovalListBloc>();
    _loadData();
  }

  @override
  void dispose() {
    _fileApprovalListBloc.close();
    super.dispose();
  }

  /// 加载数据
  void _loadData() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final state = planListBloc.state;

    if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
      _fileApprovalListBloc.add(
        LoadFileApprovalListEvent(planId: state.currentPlan!.planId),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      listener: (context, state) {
        // 当实习计划切换时，重新加载数据
        if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
          _fileApprovalListBloc.add(
            LoadFileApprovalListEvent(planId: state.currentPlan!.planId),
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: const CustomAppBar(title: '文件审批'),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 学年学期信息 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),
            // 文件审批列表
            Expanded(
              child: BlocBuilder<FileApprovalListBloc, FileApprovalListState>(
                bloc: _fileApprovalListBloc,
                builder: (context, state) {
                  if (state is FileApprovalListLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (state is FileApprovalListError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '加载失败',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            state.message,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 16.h),
                          ElevatedButton(
                            onPressed: _loadData,
                            child: const Text('重试'),
                          ),
                        ],
                      ),
                    );
                  } else if (state is FileApprovalListLoaded) {
                    if (state.fileApprovalList.isEmpty) {
                      return Center(
                        child: Text(
                          '暂无文件审批数据',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        final planListBloc = GetIt.instance<PlanListGlobalBloc>();
                        final planState = planListBloc.state;

                        if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
                          _fileApprovalListBloc.add(
                            RefreshFileApprovalListEvent(planId: planState.currentPlan!.planId),
                          );
                        }
                      },
                      child: ListView.builder(
                        padding: EdgeInsets.symmetric(horizontal: 25.w),
                        itemCount: state.fileApprovalList.length,
                        itemBuilder: (context, index) {
                          final item = state.fileApprovalList[index];
                          final fileTypeInfo = FileTypeMapper.getFileTypeInfo(item.fileType);

                          return FileListItem.withStatistics(
                            fileId: fileTypeInfo.fileId,
                            iconPath: fileTypeInfo.iconPath,
                            fileName: item.fileType, // 直接使用API返回的文件类型名称
                            statisticsText: item.statisticsText,
                            statisticsTextColor: const Color(0xFFB0B0B0),
                            onTap: () => _navigateToFileApprovalList(context, fileTypeInfo.fileId),
                          );
                        },
                      ),
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 导航到文件审批列表页面
  static void _navigateToFileApprovalList(BuildContext context, String fileId) {
    // 跳转到文件审批列表页面，使用GoRouter进行路由跳转，并携带参数fileId
    context.push('/file_approval_detail/$fileId');
  }
}
