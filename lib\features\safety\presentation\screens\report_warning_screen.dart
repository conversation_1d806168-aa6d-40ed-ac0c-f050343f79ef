/// -----
/// report_warning_screen.dart
/// 
/// 报告预警页面，用于显示学生实习报告AI检测异常的预警信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/sticky_tab_bar.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/utils/responsive_util.dart';

/// 问题类型枚举
enum ProblemType {
  /// 情绪问题
  emotional,
  /// 学习困扰
  learning,
  /// 行为异常
  behavioral,
  /// 焦虑
  anxiety,
}

/// 严重程度枚举
enum SeverityLevel {
  /// 严重
  severe,
  /// 中等
  moderate,
  /// 轻度
  mild,
}

/// 报告预警记录模型
class ReportWarningRecord {
  final String studentName;
  final String phoneNumber;
  final String avatarUrl;
  final DateTime warningTime;
  final String description;
  final ProblemType problemType;
  final SeverityLevel severityLevel;

  const ReportWarningRecord({
    required this.studentName,
    required this.phoneNumber,
    required this.avatarUrl,
    required this.warningTime,
    required this.description,
    required this.problemType,
    required this.severityLevel,
  });
}

class ReportWarningScreen extends StatefulWidget {
  const ReportWarningScreen({Key? key}) : super(key: key);

  @override
  State<ReportWarningScreen> createState() => _ReportWarningScreenState();
}

class _ReportWarningScreenState extends State<ReportWarningScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Tab标签
  final List<String> _tabs = ['全部', '情绪问题', '学习困扰', '行为异常', '焦虑'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // 模拟数据
  final List<ReportWarningRecord> _allWarningRecords = [
    ReportWarningRecord(
      studentName: '李成儒',
      phoneNumber: '13569874562',
      avatarUrl: 'https://picsum.photos/88/88?random=1',
      warningTime: DateTime(2025, 6, 11, 20, 1),
      description: '最近一周表现明显的情绪低落，实习进度缓慢，与其他人交流明显减少',
      problemType: ProblemType.emotional,
      severityLevel: SeverityLevel.severe,
    ),
    ReportWarningRecord(
      studentName: '李成儒',
      phoneNumber: '13569874562',
      avatarUrl: 'https://picsum.photos/88/88?random=2',
      warningTime: DateTime(2025, 6, 11, 20, 1),
      description: '最近一周表现明显的情绪低落，实习进度缓慢，与其他人交流明显减少，需要辅导员持续关注...',
      problemType: ProblemType.learning,
      severityLevel: SeverityLevel.moderate,
    ),
    ReportWarningRecord(
      studentName: '李成儒',
      phoneNumber: '13569874562',
      avatarUrl: 'https://picsum.photos/88/88?random=3',
      warningTime: DateTime(2025, 6, 11, 20, 1),
      description: '最近一周表现明显的情绪低落，实习进度缓慢，与其他人交流明显减少',
      problemType: ProblemType.anxiety,
      severityLevel: SeverityLevel.mild,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '报告预警',
        centerTitle: true,
        showBackButton: true,
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // 固定的Tab栏
            SliverPersistentHeader(
              pinned: true,
              delegate: StickyTabBarDelegate(
                tabBar: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.primaryColor,
                  unselectedLabelColor: const Color(0xFF666666),
                  indicatorColor: AppTheme.primaryColor,
                  indicatorWeight: 3,
                  labelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  padding: EdgeInsets.only(left: 25.w),
                  tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
                ),

              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: _tabs.asMap().entries.map((entry) {
            final index = entry.key;
            return _buildTabContent(index);
          }).toList(),
        ),
      ),
    );
  }

  /// 构建Tab内容
  Widget _buildTabContent(int tabIndex) {
    final filteredRecords = _getFilteredRecords(tabIndex);

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
      itemCount: filteredRecords.length,
      itemBuilder: (context, index) {
        return _buildReportWarningItem(filteredRecords[index]);
      },
    );
  }

  /// 根据选中的Tab过滤记录
  List<ReportWarningRecord> _getFilteredRecords(int tabIndex) {
    if (tabIndex == 0) {
      // 全部
      return _allWarningRecords;
    }

    ProblemType? filterType;
    switch (tabIndex) {
      case 1:
        filterType = ProblemType.emotional;
        break;
      case 2:
        filterType = ProblemType.learning;
        break;
      case 3:
        filterType = ProblemType.behavioral;
        break;
      case 4:
        filterType = ProblemType.anxiety;
        break;
    }

    if (filterType != null) {
      return _allWarningRecords.where((record) => record.problemType == filterType).toList();
    }

    return _allWarningRecords;
  }

  /// 构建报告预警列表项
  Widget _buildReportWarningItem(ReportWarningRecord record) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧头像
          _buildAvatar(record.studentName, avatarUrl: record.avatarUrl),
          
          SizedBox(width: 24.w),
          
          // 中间信息
          Expanded(
            child: _buildStudentInfo(record),
          ),
          
          SizedBox(width: 16.w),
          
          // 右侧严重程度标签
          _buildSeverityTag(record.severityLevel),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(String studentName, {String? avatarUrl}) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: avatarUrl != null && avatarUrl.isNotEmpty
            ? Image.network(
                avatarUrl,
                width: 88.w,
                height: 88.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar(studentName);
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return child;
                  }
                  return _buildDefaultAvatar(studentName);
                },
              )
            : _buildDefaultAvatar(studentName),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar(String studentName) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          studentName.isNotEmpty ? studentName.substring(0, 1) : '',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建学生信息
  Widget _buildStudentInfo(ReportWarningRecord record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名和电话
        Row(
          children: [
            Text(
              record.studentName,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(width: 20.w),
            Icon(
              Icons.phone,
              size: 28.w,
              color: const Color(0xFF666666),
            ),
            SizedBox(width: 8.w),
            Text(
              record.phoneNumber,
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 12.h),
        
        // 时间
        Text(
          _formatDateTime(record.warningTime),
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF999999),
          ),
        ),
        
        SizedBox(height: 16.h),
        
        // 预警描述
        Text(
          record.description,
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF333333),
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        
        SizedBox(height: 20.h),
        
        // 问题类型标签
        _buildProblemTypeTag(record.problemType),
      ],
    );
  }

  /// 构建严重程度标签
  Widget _buildSeverityTag(SeverityLevel level) {
    String text;
    Color backgroundColor;

    switch (level) {
      case SeverityLevel.severe:
        text = '严重';
        backgroundColor = const Color(0xFFFF4444);
        break;
      case SeverityLevel.moderate:
        text = '中等';
        backgroundColor = const Color(0xFFFF4444);
        break;
      case SeverityLevel.mild:
        text = '轻度';
        backgroundColor = const Color(0xFFFF9500);
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 24.sp,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建问题类型标签
  Widget _buildProblemTypeTag(ProblemType type) {
    String text;
    Color borderColor;

    switch (type) {
      case ProblemType.emotional:
        text = '情绪问题';
        borderColor = const Color(0xFFFF4444);
        break;
      case ProblemType.learning:
        text = '学习困扰';
        borderColor = const Color(0xFFFF4444);
        break;
      case ProblemType.behavioral:
        text = '行为异常';
        borderColor = const Color(0xFFFF9500);
        break;
      case ProblemType.anxiety:
        text = '焦虑';
        borderColor = const Color(0xFFFF9500);
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: borderColor, width: 1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 24.sp,
          color: borderColor,
        ),
      ),
    );
  }

  /// 格式化时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
